"""
主界面窗口
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, filedialog
import requests
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from ui.styles import ModernStyles
from config.settings import API_HOST, API_PORT, API_PREFIX, WINDOW_WIDTH, WINDOW_HEIGHT

class MainWindow:
    """主界面窗口类"""
    
    def __init__(self, user_data):
        self.user_data = user_data
        self.window = None
        self.folders_data = []
        self.current_folder = None
        self.current_files = []
        
        self.setup_window()
        self.create_widgets()
        self.load_folders()
    
    def setup_window(self):
        """设置窗口"""
        self.window = ctk.CTk()
        self.window.title(f"NewNet File Manager - 欢迎 {self.user_data['username']}")
        self.window.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = ctk.CTkFrame(
            self.window,
            fg_color="transparent"
        )
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 顶部工具栏
        self.create_toolbar(main_container)
        
        # 主内容区域
        content_frame = ctk.CTkFrame(
            main_container,
            fg_color="transparent"
        )
        content_frame.pack(fill="both", expand=True, pady=(10, 0))
        
        # 左侧边栏 - 文件夹列表
        self.create_sidebar(content_frame)
        
        # 右侧主内容区
        self.create_main_content(content_frame)
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ctk.CTkFrame(
            parent,
            **ModernStyles.get_frame_style('card'),
            height=60
        )
        toolbar.pack(fill="x", pady=(0, 10))
        toolbar.pack_propagate(False)
        
        # 左侧按钮组
        left_buttons = ctk.CTkFrame(toolbar, fg_color="transparent")
        left_buttons.pack(side="left", fill="y", padx=20, pady=10)
        
        # 添加文件夹按钮
        add_folder_btn = ctk.CTkButton(
            left_buttons,
            text="+ 添加文件夹",
            command=self.add_folder,
            **ModernStyles.get_button_style('primary'),
            width=120
        )
        add_folder_btn.pack(side="left", padx=(0, 10))
        
        # 刷新按钮
        refresh_btn = ctk.CTkButton(
            left_buttons,
            text="刷新",
            command=self.load_folders,
            **ModernStyles.get_button_style('secondary'),
            width=80
        )
        refresh_btn.pack(side="left", padx=(0, 10))
        
        # 右侧用户信息
        user_info = ctk.CTkFrame(toolbar, fg_color="transparent")
        user_info.pack(side="right", fill="y", padx=20, pady=10)
        
        # 用户名标签
        user_label = ctk.CTkLabel(
            user_info,
            text=f"用户: {self.user_data['username']}",
            **ModernStyles.get_label_style('body')
        )
        user_label.pack(side="right", padx=(10, 0))
        
        # 管理员标识
        if self.user_data.get('is_admin'):
            admin_label = ctk.CTkLabel(
                user_info,
                text="管理员",
                **ModernStyles.get_label_style('body'),
                text_color=ModernStyles.COLORS['success']
            )
            admin_label.pack(side="right")
    
    def create_sidebar(self, parent):
        """创建侧边栏"""
        sidebar = ctk.CTkFrame(
            parent,
            **ModernStyles.get_frame_style('sidebar'),
            width=300
        )
        sidebar.pack(side="left", fill="y", padx=(0, 10))
        sidebar.pack_propagate(False)
        
        # 侧边栏标题
        sidebar_title = ctk.CTkLabel(
            sidebar,
            text="文件夹列表",
            **ModernStyles.get_label_style('heading')
        )
        sidebar_title.pack(pady=(20, 10), padx=20)
        
        # 文件夹列表容器
        self.folders_frame = ctk.CTkScrollableFrame(
            sidebar,
            **ModernStyles.get_scrollable_frame_style()
        )
        self.folders_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    
    def create_main_content(self, parent):
        """创建主内容区"""
        main_content = ctk.CTkFrame(
            parent,
            **ModernStyles.get_frame_style('card')
        )
        main_content.pack(side="right", fill="both", expand=True)
        
        # 内容标题区
        title_frame = ctk.CTkFrame(main_content, fg_color="transparent")
        title_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        self.content_title = ctk.CTkLabel(
            title_frame,
            text="请选择文件夹",
            **ModernStyles.get_label_style('heading')
        )
        self.content_title.pack(side="left")
        
        # 文件列表容器
        self.files_frame = ctk.CTkScrollableFrame(
            main_content,
            **ModernStyles.get_scrollable_frame_style()
        )
        self.files_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # 默认提示
        self.show_welcome_message()
    
    def show_welcome_message(self):
        """显示欢迎信息"""
        welcome_frame = ctk.CTkFrame(self.files_frame, fg_color="transparent")
        welcome_frame.pack(expand=True, fill="both")
        
        welcome_label = ctk.CTkLabel(
            welcome_frame,
            text="欢迎使用 NewNet File Manager\n\n请从左侧选择文件夹查看文件",
            **ModernStyles.get_label_style('secondary'),
            justify="center"
        )
        welcome_label.pack(expand=True)
    
    def add_folder(self):
        """添加文件夹"""
        # 选择文件夹
        folder_path = filedialog.askdirectory(title="选择要添加的文件夹")
        if not folder_path:
            return
        
        # 获取文件夹名称
        folder_name = os.path.basename(folder_path)
        
        # 简单的名称输入对话框
        dialog = ctk.CTkInputDialog(
            text=f"请输入文件夹显示名称:",
            title="添加文件夹"
        )
        display_name = dialog.get_input()
        
        if not display_name:
            display_name = folder_name
        
        try:
            # 调用API添加文件夹
            response = requests.post(
                f"http://{API_HOST}:{API_PORT}{API_PREFIX}/folders/",
                json={
                    "name": display_name,
                    "path": folder_path,
                    "description": f"文件夹路径: {folder_path}"
                },
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    messagebox.showinfo("成功", "文件夹添加成功!")
                    self.load_folders()
                else:
                    messagebox.showerror("错误", data.get('message', '添加失败'))
            else:
                messagebox.showerror("错误", "服务器连接失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"添加文件夹失败: {str(e)}")
    
    def load_folders(self):
        """加载文件夹列表"""
        try:
            response = requests.get(
                f"http://{API_HOST}:{API_PORT}{API_PREFIX}/folders/",
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.folders_data = data.get('data', {}).get('folders', [])
                    self.update_folders_display()
                else:
                    messagebox.showerror("错误", data.get('message', '加载失败'))
            else:
                messagebox.showerror("错误", "服务器连接失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载文件夹失败: {str(e)}")
    
    def update_folders_display(self):
        """更新文件夹显示"""
        # 清空现有内容
        for widget in self.folders_frame.winfo_children():
            widget.destroy()
        
        # 添加文件夹项
        for folder in self.folders_data:
            self.create_folder_item(folder)
    
    def create_folder_item(self, folder):
        """创建文件夹项"""
        folder_frame = ctk.CTkFrame(
            self.folders_frame,
            **ModernStyles.get_frame_style('card'),
            height=80
        )
        folder_frame.pack(fill="x", pady=5)
        folder_frame.pack_propagate(False)
        
        # 文件夹信息
        info_frame = ctk.CTkFrame(folder_frame, fg_color="transparent")
        info_frame.pack(fill="both", expand=True, padx=15, pady=10)
        
        # 文件夹名称
        name_label = ctk.CTkLabel(
            info_frame,
            text=folder['name'],
            **ModernStyles.get_label_style('subheading')
        )
        name_label.pack(anchor="w")
        
        # 文件夹路径
        path_label = ctk.CTkLabel(
            info_frame,
            text=folder['path'],
            **ModernStyles.get_label_style('muted')
        )
        path_label.pack(anchor="w", pady=(2, 0))
        
        # 点击事件
        def on_click(event, f=folder):
            self.select_folder(f)
        
        folder_frame.bind("<Button-1>", on_click)
        info_frame.bind("<Button-1>", on_click)
        name_label.bind("<Button-1>", on_click)
        path_label.bind("<Button-1>", on_click)
        
        # 悬停效果
        def on_enter(event):
            folder_frame.configure(fg_color=ModernStyles.COLORS['bg_hover'])
        
        def on_leave(event):
            folder_frame.configure(fg_color=ModernStyles.COLORS['bg_card'])
        
        folder_frame.bind("<Enter>", on_enter)
        folder_frame.bind("<Leave>", on_leave)

    def select_folder(self, folder):
        """选择文件夹"""
        self.current_folder = folder
        self.content_title.configure(text=f"文件夹: {folder['name']}")
        self.load_folder_files(folder['id'])

    def load_folder_files(self, folder_id):
        """加载文件夹中的文件"""
        try:
            response = requests.get(
                f"http://{API_HOST}:{API_PORT}{API_PREFIX}/folders/{folder_id}/files",
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    folder_data = data.get('data', {})
                    self.current_files = folder_data.get('files', [])
                    self.update_files_display()
                else:
                    messagebox.showerror("错误", data.get('message', '加载文件失败'))
            else:
                messagebox.showerror("错误", "服务器连接失败")

        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)}")

    def update_files_display(self):
        """更新文件显示"""
        # 清空现有内容
        for widget in self.files_frame.winfo_children():
            widget.destroy()

        if not self.current_files:
            # 显示空文件夹提示
            empty_frame = ctk.CTkFrame(self.files_frame, fg_color="transparent")
            empty_frame.pack(expand=True, fill="both")

            empty_label = ctk.CTkLabel(
                empty_frame,
                text="此文件夹为空",
                **ModernStyles.get_label_style('secondary')
            )
            empty_label.pack(expand=True)
            return

        # 创建文件列表标题
        header_frame = ctk.CTkFrame(
            self.files_frame,
            **ModernStyles.get_frame_style('card'),
            height=40
        )
        header_frame.pack(fill="x", pady=(0, 10))
        header_frame.pack_propagate(False)

        # 标题列
        headers = [
            ("文件名", 0.4),
            ("大小", 0.15),
            ("类型", 0.15),
            ("操作", 0.3)
        ]

        for i, (header_text, width_ratio) in enumerate(headers):
            header_label = ctk.CTkLabel(
                header_frame,
                text=header_text,
                **ModernStyles.get_label_style('subheading')
            )
            header_label.place(relx=sum(h[1] for h in headers[:i]), rely=0.5,
                             relwidth=width_ratio, anchor="w")

        # 添加文件项
        for file_info in self.current_files:
            self.create_file_item(file_info)

    def create_file_item(self, file_info):
        """创建文件项"""
        file_frame = ctk.CTkFrame(
            self.files_frame,
            **ModernStyles.get_frame_style('card'),
            height=60
        )
        file_frame.pack(fill="x", pady=2)
        file_frame.pack_propagate(False)

        # 文件名
        name_label = ctk.CTkLabel(
            file_frame,
            text=file_info['name'],
            **ModernStyles.get_label_style('body')
        )
        name_label.place(relx=0, rely=0.5, relwidth=0.4, anchor="w")

        # 文件大小
        size_text = self.format_file_size(file_info['size'])
        size_label = ctk.CTkLabel(
            file_frame,
            text=size_text,
            **ModernStyles.get_label_style('secondary')
        )
        size_label.place(relx=0.4, rely=0.5, relwidth=0.15, anchor="w")

        # 文件类型
        ext_text = file_info['extension'].upper() if file_info['extension'] else "文件"
        ext_label = ctk.CTkLabel(
            file_frame,
            text=ext_text,
            **ModernStyles.get_label_style('secondary')
        )
        ext_label.place(relx=0.55, rely=0.5, relwidth=0.15, anchor="w")

        # 操作按钮
        action_frame = ctk.CTkFrame(file_frame, fg_color="transparent")
        action_frame.place(relx=0.7, rely=0.5, relwidth=0.3, anchor="w")

        # 下载按钮
        download_btn = ctk.CTkButton(
            action_frame,
            text="下载",
            command=lambda f=file_info: self.download_file(f),
            **ModernStyles.get_button_style('primary'),
            width=80,
            height=30
        )
        download_btn.pack(side="left", padx=5)

        # 查看按钮
        view_btn = ctk.CTkButton(
            action_frame,
            text="查看",
            command=lambda f=file_info: self.view_file(f),
            **ModernStyles.get_button_style('secondary'),
            width=80,
            height=30
        )
        view_btn.pack(side="left", padx=5)

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def download_file(self, file_info):
        """下载文件"""
        try:
            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                title="保存文件",
                initialname=file_info['name'],
                defaultextension=file_info['extension']
            )

            if not save_path:
                return

            # 复制文件
            import shutil
            shutil.copy2(file_info['path'], save_path)

            # 记录下载日志
            self.record_download(file_info)

            messagebox.showinfo("成功", f"文件已保存到: {save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"下载失败: {str(e)}")

    def view_file(self, file_info):
        """查看文件"""
        try:
            # 使用系统默认程序打开文件
            import subprocess
            import platform

            if platform.system() == 'Windows':
                os.startfile(file_info['path'])
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', file_info['path']])
            else:  # Linux
                subprocess.call(['xdg-open', file_info['path']])

        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件: {str(e)}")

    def record_download(self, file_info):
        """记录下载"""
        try:
            if not self.current_folder:
                return

            requests.post(
                f"http://{API_HOST}:{API_PORT}{API_PREFIX}/permissions/download",
                json={
                    "user_id": self.user_data['user_id'],
                    "folder_id": self.current_folder['id'],
                    "file_path": file_info['path']
                },
                timeout=5
            )
        except:
            pass  # 静默处理下载记录失败

    def show(self):
        """显示窗口"""
        self.window.mainloop()

if __name__ == "__main__":
    # 测试主界面
    test_user = {
        'user_id': 1,
        'username': 'admin',
        'is_admin': True
    }
    main_window = MainWindow(test_user)
    main_window.show()
