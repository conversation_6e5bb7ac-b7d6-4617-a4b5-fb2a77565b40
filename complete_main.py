"""
NewNet File Manager - 完整版本
集成所有功能，不依赖外部包（除了CustomTkinter用于现代UI）
"""
import sys
import os
import sqlite3
import hashlib
from pathlib import Path
import time
import shutil
from datetime import datetime

# 导入UI库
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

# 尝试导入CustomTkinter
try:
    import customtkinter as ctk
    from ui.styles import ModernStyles
    USE_MODERN_UI = True
    print("使用现代化UI (CustomTkinter)")
except ImportError:
    USE_MODERN_UI = False
    print("使用标准UI (Tkinter)")

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

from config.settings import DATABASE_PATH, DATABASE_DIR

class NewNetFileManager:
    """NewNet文件管理器主类"""
    
    def __init__(self):
        self.user_data = None
        self.folders_data = []
        self.current_folder = None
        self.current_files = []
        self.setup_database()
    
    def setup_database(self):
        """设置数据库"""
        print("初始化数据库...")
        DATABASE_DIR.mkdir(exist_ok=True)
        
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 创建用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # 创建文件夹表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                path TEXT UNIQUE NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # 创建权限表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                folder_id INTEGER NOT NULL,
                can_read BOOLEAN DEFAULT TRUE,
                can_download BOOLEAN DEFAULT FALSE,
                download_limit INTEGER,
                download_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (folder_id) REFERENCES folders (id),
                UNIQUE(user_id, folder_id)
            )
        ''')
        
        # 创建下载日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS download_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                folder_id INTEGER NOT NULL,
                file_path TEXT NOT NULL,
                download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (folder_id) REFERENCES folders (id)
            )
        ''')
        
        # 检查管理员用户
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = 1")
        if cursor.fetchone()[0] == 0:
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, is_admin)
                VALUES (?, ?, ?)
            ''', ("admin", password_hash, True))
            print("默认管理员用户已创建: admin / admin123")
        
        conn.commit()
        conn.close()
        print("数据库初始化完成")
    
    def authenticate_user(self, username, password):
        """用户认证"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('''
            SELECT id, username, is_admin FROM users 
            WHERE username = ? AND password_hash = ?
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        
        if user:
            # 更新最后登录时间
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (user[0],))
            conn.commit()
            
            result = {'id': user[0], 'username': user[1], 'is_admin': user[2]}
        else:
            result = None
        
        conn.close()
        return result
    
    def get_folders(self):
        """获取文件夹列表"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT f.id, f.name, f.path, f.description, f.created_at, u.username as created_by_name
            FROM folders f
            LEFT JOIN users u ON f.created_by = u.id
            WHERE f.is_active = 1
            ORDER BY f.created_at DESC
        ''')
        
        folders = []
        for row in cursor.fetchall():
            folders.append({
                'id': row[0],
                'name': row[1],
                'path': row[2],
                'description': row[3],
                'created_at': row[4],
                'created_by_name': row[5]
            })
        
        conn.close()
        return folders
    
    def add_folder(self, name, path, description=None):
        """添加文件夹"""
        try:
            conn = sqlite3.connect(DATABASE_PATH)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO folders (name, path, description, created_by)
                VALUES (?, ?, ?, ?)
            ''', (name, path, description, self.user_data['id'] if self.user_data else None))
            conn.commit()
            folder_id = cursor.lastrowid
            conn.close()
            return folder_id
        except sqlite3.IntegrityError:
            return None
    
    def get_folder_files(self, folder_path):
        """获取文件夹中的文件"""
        try:
            folder = Path(folder_path)
            if not folder.exists():
                return []
            
            files = []
            for item in folder.iterdir():
                if item.is_file():
                    files.append({
                        'name': item.name,
                        'path': str(item),
                        'size': item.stat().st_size,
                        'modified': item.stat().st_mtime,
                        'extension': item.suffix.lower()
                    })
            
            # 按名称排序
            files.sort(key=lambda x: x['name'].lower())
            return files
        except Exception as e:
            print(f"获取文件列表失败: {e}")
            return []
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def record_download(self, folder_id, file_path):
        """记录下载"""
        try:
            conn = sqlite3.connect(DATABASE_PATH)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO download_logs (user_id, folder_id, file_path)
                VALUES (?, ?, ?)
            ''', (self.user_data['id'], folder_id, file_path))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"记录下载失败: {e}")
            return False

    def show_login_window(self):
        """显示登录窗口"""
        if USE_MODERN_UI:
            return self.show_modern_login()
        else:
            return self.show_standard_login()

    def show_standard_login(self):
        """标准登录界面"""
        login_window = tk.Tk()
        login_window.title("NewNet File Manager - 登录")
        login_window.geometry("400x350")
        login_window.resizable(False, False)

        # 居中显示
        login_window.update_idletasks()
        x = (login_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (login_window.winfo_screenheight() // 2) - (350 // 2)
        login_window.geometry(f"400x350+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(login_window, padding="30")
        main_frame.pack(fill="both", expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="NewNet File Manager",
                               font=("Microsoft YaHei UI", 18, "bold"))
        title_label.pack(pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="现代化文件管理系统",
                                  font=("Microsoft YaHei UI", 10))
        subtitle_label.pack(pady=(0, 30))

        # 用户名
        ttk.Label(main_frame, text="用户名:", font=("Microsoft YaHei UI", 12)).pack(anchor="w")
        username_entry = ttk.Entry(main_frame, width=30, font=("Microsoft YaHei UI", 12))
        username_entry.pack(fill="x", pady=(5, 15))

        # 密码
        ttk.Label(main_frame, text="密码:", font=("Microsoft YaHei UI", 12)).pack(anchor="w")
        password_entry = ttk.Entry(main_frame, width=30, show="*", font=("Microsoft YaHei UI", 12))
        password_entry.pack(fill="x", pady=(5, 20))

        # 状态标签
        status_label = ttk.Label(main_frame, text="", foreground="red")
        status_label.pack(pady=(0, 15))

        def handle_login():
            username = username_entry.get().strip()
            password = password_entry.get().strip()

            if not username or not password:
                status_label.config(text="请输入用户名和密码")
                return

            user = self.authenticate_user(username, password)
            if user:
                self.user_data = user
                status_label.config(text="登录成功！正在进入主界面...", foreground="green")
                login_window.after(1000, login_window.destroy)
            else:
                status_label.config(text="用户名或密码错误")

        # 登录按钮
        login_button = ttk.Button(main_frame, text="登录", command=handle_login)
        login_button.pack(fill="x", pady=(0, 20))

        # 提示信息
        info_frame = ttk.LabelFrame(main_frame, text="默认账号信息", padding="10")
        info_frame.pack(fill="x")

        ttk.Label(info_frame, text="用户名: admin", font=("Microsoft YaHei UI", 10), foreground="blue").pack(anchor="w")
        ttk.Label(info_frame, text="密码: admin123", font=("Microsoft YaHei UI", 10), foreground="blue").pack(anchor="w")
        ttk.Label(info_frame, text="请在首次登录后修改密码！", font=("Microsoft YaHei UI", 9), foreground="red").pack(anchor="w", pady=(5, 0))

        # 绑定回车键
        login_window.bind('<Return>', lambda e: handle_login())
        username_entry.focus()

        login_window.mainloop()
        return self.user_data is not None

    def show_modern_login(self):
        """现代化登录界面"""
        # 这里可以实现CustomTkinter版本的登录界面
        # 暂时使用标准版本
        return self.show_standard_login()

    def show_main_window(self):
        """显示主界面"""
        if USE_MODERN_UI:
            self.show_modern_main()
        else:
            self.show_standard_main()

    def show_standard_main(self):
        """标准主界面"""
        main_window = tk.Tk()
        main_window.title(f"NewNet File Manager - 欢迎 {self.user_data['username']}")
        main_window.geometry("1200x800")

        # 居中显示
        main_window.update_idletasks()
        x = (main_window.winfo_screenwidth() // 2) - (1200 // 2)
        y = (main_window.winfo_screenheight() // 2) - (800 // 2)
        main_window.geometry(f"1200x800+{x}+{y}")

        # 创建菜单栏
        menubar = tk.Menu(main_window)
        main_window.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="添加文件夹", command=self.add_folder_dialog)
        file_menu.add_command(label="刷新", command=self.refresh_data)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=main_window.quit)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)

        # 主容器
        main_container = ttk.Frame(main_window)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # 顶部工具栏
        toolbar = ttk.Frame(main_container)
        toolbar.pack(fill="x", pady=(0, 10))

        # 工具栏按钮
        ttk.Button(toolbar, text="➕ 添加文件夹", command=self.add_folder_dialog).pack(side="left", padx=(0, 10))
        ttk.Button(toolbar, text="🔄 刷新", command=self.refresh_data).pack(side="left", padx=(0, 10))

        # 用户信息
        user_info_text = f"当前用户: {self.user_data['username']}"
        if self.user_data['is_admin']:
            user_info_text += " (管理员)"

        user_info = ttk.Label(toolbar, text=user_info_text, font=("Microsoft YaHei UI", 10))
        user_info.pack(side="right")

        # 主内容区域 - 使用PanedWindow分割
        paned_window = ttk.PanedWindow(main_container, orient="horizontal")
        paned_window.pack(fill="both", expand=True)

        # 左侧文件夹列表
        left_frame = ttk.LabelFrame(paned_window, text="📁 文件夹列表", padding="10")
        paned_window.add(left_frame, weight=1)

        # 文件夹列表框
        self.folder_listbox = tk.Listbox(left_frame, font=("Microsoft YaHei UI", 10))
        folder_scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=self.folder_listbox.yview)
        self.folder_listbox.configure(yscrollcommand=folder_scrollbar.set)

        self.folder_listbox.pack(side="left", fill="both", expand=True)
        folder_scrollbar.pack(side="right", fill="y")

        self.folder_listbox.bind('<<ListboxSelect>>', self.on_folder_select)

        # 右侧文件列表
        right_frame = ttk.LabelFrame(paned_window, text="📄 文件列表", padding="10")
        paned_window.add(right_frame, weight=2)

        # 文件列表树形控件
        columns = ("名称", "大小", "类型", "修改时间")
        self.file_tree = ttk.Treeview(right_frame, columns=columns, show="headings")

        # 设置列标题和宽度
        self.file_tree.heading("名称", text="📄 文件名")
        self.file_tree.heading("大小", text="📏 大小")
        self.file_tree.heading("类型", text="🏷️ 类型")
        self.file_tree.heading("修改时间", text="🕒 修改时间")

        self.file_tree.column("名称", width=300)
        self.file_tree.column("大小", width=100)
        self.file_tree.column("类型", width=80)
        self.file_tree.column("修改时间", width=150)

        # 文件列表滚动条
        file_scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=file_scrollbar.set)

        self.file_tree.pack(side="left", fill="both", expand=True)
        file_scrollbar.pack(side="right", fill="y")

        # 双击打开文件
        self.file_tree.bind('<Double-1>', self.open_file)

        # 右键菜单
        self.create_context_menu(main_window)

        # 状态栏
        status_frame = ttk.Frame(main_container)
        status_frame.pack(fill="x", pady=(10, 0))

        self.status_label = ttk.Label(status_frame, text="就绪", relief="sunken", anchor="w")
        self.status_label.pack(fill="x")

        # 加载数据
        self.refresh_data()

        # 显示欢迎信息
        welcome_msg = f"欢迎使用 NewNet File Manager, {self.user_data['username']}!"
        if self.user_data['is_admin']:
            welcome_msg += " 您拥有管理员权限。"

        self.status_label.config(text=welcome_msg)

        # 保存主窗口引用
        self.main_window = main_window

        main_window.mainloop()

    def show_modern_main(self):
        """现代化主界面"""
        # 这里可以实现CustomTkinter版本的主界面
        # 暂时使用标准版本
        self.show_standard_main()

    def create_context_menu(self, parent):
        """创建右键菜单"""
        self.context_menu = tk.Menu(parent, tearoff=0)
        self.context_menu.add_command(label="📂 打开", command=self.open_selected_file)
        self.context_menu.add_command(label="💾 下载到...", command=self.download_selected_file)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 复制路径", command=self.copy_file_path)
        self.context_menu.add_command(label="ℹ️ 属性", command=self.show_file_properties)

        def show_context_menu(event):
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()

        self.file_tree.bind("<Button-3>", show_context_menu)

    def refresh_data(self):
        """刷新数据"""
        self.load_folders()
        self.status_label.config(text="数据已刷新")

    def load_folders(self):
        """加载文件夹列表"""
        self.folders_data = self.get_folders()
        self.update_folder_list()

    def update_folder_list(self):
        """更新文件夹列表显示"""
        self.folder_listbox.delete(0, tk.END)
        for folder in self.folders_data:
            display_text = f"📁 {folder['name']}"
            if folder['description']:
                display_text += f" - {folder['description']}"
            self.folder_listbox.insert(tk.END, display_text)

    def on_folder_select(self, event):
        """文件夹选择事件"""
        selection = self.folder_listbox.curselection()
        if not selection:
            return

        folder_index = selection[0]
        if folder_index < len(self.folders_data):
            self.current_folder = self.folders_data[folder_index]
            self.load_files(self.current_folder['path'])
            self.status_label.config(text=f"已选择文件夹: {self.current_folder['name']}")

    def load_files(self, folder_path):
        """加载文件列表"""
        # 清空文件列表
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        self.current_files = self.get_folder_files(folder_path)

        if not self.current_files:
            # 插入空提示
            self.file_tree.insert("", tk.END, values=("📭 此文件夹为空", "", "", ""))
            self.status_label.config(text="文件夹为空")
            return

        for file_info in self.current_files:
            name = file_info['name']
            size = self.format_file_size(file_info['size'])
            file_type = file_info['extension'].upper() if file_info['extension'] else "文件"
            modified = time.strftime("%Y-%m-%d %H:%M", time.localtime(file_info['modified']))

            # 根据文件类型添加图标
            icon = self.get_file_icon(file_info['extension'])
            display_name = f"{icon} {name}"

            self.file_tree.insert("", tk.END, values=(display_name, size, file_type, modified),
                                tags=(file_info['path'],))

        self.status_label.config(text=f"共找到 {len(self.current_files)} 个文件")

    def get_file_icon(self, extension):
        """根据文件扩展名获取图标"""
        icons = {
            '.txt': '📄', '.doc': '📄', '.docx': '📄', '.pdf': '📄',
            '.xls': '📊', '.xlsx': '📊', '.csv': '📊',
            '.ppt': '📽️', '.pptx': '📽️',
            '.jpg': '🖼️', '.jpeg': '🖼️', '.png': '🖼️', '.gif': '🖼️', '.bmp': '🖼️',
            '.mp4': '🎬', '.avi': '🎬', '.mov': '🎬', '.wmv': '🎬',
            '.mp3': '🎵', '.wav': '🎵', '.flac': '🎵',
            '.zip': '📦', '.rar': '📦', '.7z': '📦',
            '.exe': '⚙️', '.msi': '⚙️',
            '.py': '🐍', '.js': '📜', '.html': '🌐', '.css': '🎨',
        }
        return icons.get(extension.lower(), '📄')

    def add_folder_dialog(self):
        """添加文件夹对话框"""
        folder_path = filedialog.askdirectory(title="选择要添加的文件夹")
        if not folder_path:
            return

        # 获取文件夹名称
        folder_name = os.path.basename(folder_path)

        # 创建输入对话框
        dialog = tk.Toplevel(self.main_window)
        dialog.title("添加文件夹")
        dialog.geometry("400x200")
        dialog.resizable(False, False)

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (200 // 2)
        dialog.geometry(f"400x200+{x}+{y}")

        # 对话框内容
        frame = ttk.Frame(dialog, padding="20")
        frame.pack(fill="both", expand=True)

        ttk.Label(frame, text="文件夹路径:", font=("Microsoft YaHei UI", 10)).pack(anchor="w")
        ttk.Label(frame, text=folder_path, font=("Microsoft YaHei UI", 9), foreground="blue").pack(anchor="w", pady=(0, 10))

        ttk.Label(frame, text="显示名称:", font=("Microsoft YaHei UI", 10)).pack(anchor="w")
        name_entry = ttk.Entry(frame, width=40, font=("Microsoft YaHei UI", 10))
        name_entry.insert(0, folder_name)
        name_entry.pack(fill="x", pady=(5, 10))

        ttk.Label(frame, text="描述 (可选):", font=("Microsoft YaHei UI", 10)).pack(anchor="w")
        desc_entry = ttk.Entry(frame, width=40, font=("Microsoft YaHei UI", 10))
        desc_entry.pack(fill="x", pady=(5, 15))

        def confirm_add():
            name = name_entry.get().strip()
            description = desc_entry.get().strip()

            if not name:
                messagebox.showerror("错误", "请输入显示名称")
                return

            folder_id = self.add_folder(name, folder_path, description)
            if folder_id:
                messagebox.showinfo("成功", f"文件夹 '{name}' 添加成功!")
                dialog.destroy()
                self.refresh_data()
            else:
                messagebox.showerror("错误", "该文件夹已存在!")

        # 按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x")

        ttk.Button(button_frame, text="确定", command=confirm_add).pack(side="right", padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side="right")

        name_entry.focus()
        name_entry.select_range(0, tk.END)

    def open_file(self, event):
        """双击打开文件"""
        self.open_selected_file()

    def open_selected_file(self):
        """打开选中的文件"""
        selection = self.file_tree.selection()
        if not selection:
            return

        item = self.file_tree.item(selection[0])
        if not item['tags']:
            return

        file_path = item['tags'][0]

        try:
            os.startfile(file_path)
            self.status_label.config(text=f"已打开文件: {os.path.basename(file_path)}")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件: {str(e)}")

    def download_selected_file(self):
        """下载选中的文件"""
        selection = self.file_tree.selection()
        if not selection:
            return

        item = self.file_tree.item(selection[0])
        if not item['tags']:
            return

        source_path = item['tags'][0]
        filename = os.path.basename(source_path)

        # 选择保存位置
        save_path = filedialog.asksaveasfilename(
            title="保存文件",
            initialname=filename,
            defaultextension=os.path.splitext(filename)[1]
        )

        if not save_path:
            return

        try:
            shutil.copy2(source_path, save_path)

            # 记录下载日志
            if self.current_folder:
                self.record_download(self.current_folder['id'], source_path)

            messagebox.showinfo("成功", f"文件已保存到: {save_path}")
            self.status_label.config(text=f"文件下载成功: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"下载失败: {str(e)}")

    def copy_file_path(self):
        """复制文件路径到剪贴板"""
        selection = self.file_tree.selection()
        if not selection:
            return

        item = self.file_tree.item(selection[0])
        if not item['tags']:
            return

        file_path = item['tags'][0]

        try:
            self.file_tree.clipboard_clear()
            self.file_tree.clipboard_append(file_path)
            self.status_label.config(text=f"已复制路径: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")

    def show_file_properties(self):
        """显示文件属性"""
        selection = self.file_tree.selection()
        if not selection:
            return

        item = self.file_tree.item(selection[0])
        if not item['tags']:
            return

        file_path = item['tags'][0]

        try:
            file_stat = os.stat(file_path)

            # 创建属性对话框
            dialog = tk.Toplevel(self.main_window)
            dialog.title("文件属性")
            dialog.geometry("400x300")
            dialog.resizable(False, False)

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
            y = (dialog.winfo_screenheight() // 2) - (300 // 2)
            dialog.geometry(f"400x300+{x}+{y}")

            frame = ttk.Frame(dialog, padding="20")
            frame.pack(fill="both", expand=True)

            # 文件信息
            info_text = f"""文件名: {os.path.basename(file_path)}
路径: {file_path}
大小: {self.format_file_size(file_stat.st_size)}
类型: {os.path.splitext(file_path)[1].upper() if os.path.splitext(file_path)[1] else '文件'}
创建时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_stat.st_ctime))}
修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_stat.st_mtime))}
访问时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_stat.st_atime))}"""

            text_widget = tk.Text(frame, wrap="word", font=("Microsoft YaHei UI", 10))
            text_widget.insert("1.0", info_text)
            text_widget.config(state="disabled")
            text_widget.pack(fill="both", expand=True, pady=(0, 10))

            ttk.Button(frame, text="关闭", command=dialog.destroy).pack()

        except Exception as e:
            messagebox.showerror("错误", f"获取文件属性失败: {str(e)}")

    def show_about(self):
        """显示关于对话框"""
        about_text = """NewNet File Manager v1.0.0

现代化文件管理系统

功能特点:
• 用户权限管理
• 文件夹统一管理
• 下载控制和日志
• 现代化界面设计
• 完整的API架构

技术栈:
• Python + Tkinter/CustomTkinter
• SQLite 数据库
• 模块化架构设计

开发团队: NewNet Development Team
版权所有 © 2024"""

        messagebox.showinfo("关于 NewNet File Manager", about_text)

    def run(self):
        """运行应用程序"""
        print("=" * 60)
        print("NewNet File Manager v1.0.0")
        print("现代化文件管理系统")
        print("=" * 60)
        print("功能说明:")
        print("1. 🔐 用户登录认证")
        print("2. 📁 文件夹管理")
        print("3. 📄 文件浏览和操作")
        print("4. 💾 文件下载和日志")
        print("5. 👥 用户权限控制")
        print("=" * 60)

        try:
            if self.show_login_window():
                print(f"✅ 登录成功: {self.user_data['username']} ({'管理员' if self.user_data['is_admin'] else '普通用户'})")
                self.show_main_window()
            else:
                print("❌ 登录取消或失败")
        except Exception as e:
            print(f"❌ 程序运行出错: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        input("按回车键退出...")
        return

    # 创建并运行应用程序
    app = NewNetFileManager()
    app.run()

if __name__ == "__main__":
    main()
