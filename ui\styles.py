"""
UI样式定义 - 现代化设计风格
"""
import customtkinter as ctk

# 设置外观模式和颜色主题
ctk.set_appearance_mode("light")  # 明亮主题
ctk.set_default_color_theme("blue")  # 蓝色主题

class ModernStyles:
    """现代化UI样式类"""
    
    # 颜色配置
    COLORS = {
        # 主色调
        'primary': '#2563eb',      # 蓝色
        'primary_hover': '#1d4ed8',
        'primary_light': '#dbeafe',
        
        # 背景色
        'bg_primary': '#ffffff',    # 主背景 - 白色
        'bg_secondary': '#f8fafc',  # 次要背景 - 浅灰
        'bg_card': '#ffffff',       # 卡片背景
        'bg_hover': '#f1f5f9',      # 悬停背景
        
        # 文字颜色
        'text_primary': '#1e293b',   # 主文字 - 深灰
        'text_secondary': '#64748b', # 次要文字 - 中灰
        'text_muted': '#94a3b8',     # 弱化文字 - 浅灰
        'text_white': '#ffffff',     # 白色文字
        
        # 边框颜色
        'border': '#e2e8f0',        # 边框色
        'border_focus': '#2563eb',   # 焦点边框
        
        # 状态颜色
        'success': '#10b981',        # 成功 - 绿色
        'warning': '#f59e0b',        # 警告 - 橙色
        'error': '#ef4444',          # 错误 - 红色
        'info': '#3b82f6',           # 信息 - 蓝色
    }
    
    # 字体配置
    FONTS = {
        'title': ('Microsoft YaHei UI', 24, 'bold'),
        'heading': ('Microsoft YaHei UI', 18, 'bold'),
        'subheading': ('Microsoft YaHei UI', 14, 'bold'),
        'body': ('Microsoft YaHei UI', 12, 'normal'),
        'small': ('Microsoft YaHei UI', 10, 'normal'),
        'button': ('Microsoft YaHei UI', 12, 'bold'),
    }
    
    # 间距配置
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 16,
        'lg': 24,
        'xl': 32,
        'xxl': 48,
    }
    
    # 圆角配置
    RADIUS = {
        'sm': 4,
        'md': 8,
        'lg': 12,
        'xl': 16,
        'full': 999,
    }
    
    @classmethod
    def get_button_style(cls, variant='primary'):
        """获取按钮样式"""
        styles = {
            'primary': {
                'fg_color': cls.COLORS['primary'],
                'hover_color': cls.COLORS['primary_hover'],
                'text_color': cls.COLORS['text_white'],
                'font': cls.FONTS['button'],
                'corner_radius': cls.RADIUS['md'],
                'height': 40,
            },
            'secondary': {
                'fg_color': cls.COLORS['bg_secondary'],
                'hover_color': cls.COLORS['bg_hover'],
                'text_color': cls.COLORS['text_primary'],
                'font': cls.FONTS['button'],
                'corner_radius': cls.RADIUS['md'],
                'height': 40,
                'border_width': 1,
                'border_color': cls.COLORS['border'],
            },
            'success': {
                'fg_color': cls.COLORS['success'],
                'hover_color': '#059669',
                'text_color': cls.COLORS['text_white'],
                'font': cls.FONTS['button'],
                'corner_radius': cls.RADIUS['md'],
                'height': 40,
            },
            'danger': {
                'fg_color': cls.COLORS['error'],
                'hover_color': '#dc2626',
                'text_color': cls.COLORS['text_white'],
                'font': cls.FONTS['button'],
                'corner_radius': cls.RADIUS['md'],
                'height': 40,
            }
        }
        return styles.get(variant, styles['primary'])
    
    @classmethod
    def get_entry_style(cls):
        """获取输入框样式"""
        return {
            'font': cls.FONTS['body'],
            'corner_radius': cls.RADIUS['md'],
            'border_width': 1,
            'border_color': cls.COLORS['border'],
            'fg_color': cls.COLORS['bg_primary'],
            'text_color': cls.COLORS['text_primary'],
            'height': 40,
        }
    
    @classmethod
    def get_frame_style(cls, variant='default'):
        """获取框架样式"""
        styles = {
            'default': {
                'fg_color': cls.COLORS['bg_primary'],
                'corner_radius': cls.RADIUS['lg'],
            },
            'card': {
                'fg_color': cls.COLORS['bg_card'],
                'corner_radius': cls.RADIUS['lg'],
                'border_width': 1,
                'border_color': cls.COLORS['border'],
            },
            'sidebar': {
                'fg_color': cls.COLORS['bg_secondary'],
                'corner_radius': 0,
            }
        }
        return styles.get(variant, styles['default'])
    
    @classmethod
    def get_label_style(cls, variant='body'):
        """获取标签样式"""
        styles = {
            'title': {
                'font': cls.FONTS['title'],
                'text_color': cls.COLORS['text_primary'],
            },
            'heading': {
                'font': cls.FONTS['heading'],
                'text_color': cls.COLORS['text_primary'],
            },
            'subheading': {
                'font': cls.FONTS['subheading'],
                'text_color': cls.COLORS['text_primary'],
            },
            'body': {
                'font': cls.FONTS['body'],
                'text_color': cls.COLORS['text_primary'],
            },
            'secondary': {
                'font': cls.FONTS['body'],
                'text_color': cls.COLORS['text_secondary'],
            },
            'muted': {
                'font': cls.FONTS['small'],
                'text_color': cls.COLORS['text_muted'],
            }
        }
        return styles.get(variant, styles['body'])
    
    @classmethod
    def get_scrollable_frame_style(cls):
        """获取可滚动框架样式"""
        return {
            'fg_color': cls.COLORS['bg_primary'],
            'corner_radius': cls.RADIUS['lg'],
            'scrollbar_fg_color': cls.COLORS['bg_secondary'],
            'scrollbar_button_color': cls.COLORS['primary'],
            'scrollbar_button_hover_color': cls.COLORS['primary_hover'],
        }
    
    @classmethod
    def get_tabview_style(cls):
        """获取选项卡样式"""
        return {
            'fg_color': cls.COLORS['bg_primary'],
            'segmented_button_fg_color': cls.COLORS['bg_secondary'],
            'segmented_button_selected_color': cls.COLORS['primary'],
            'segmented_button_selected_hover_color': cls.COLORS['primary_hover'],
            'text_color': cls.COLORS['text_primary'],
            'text_color_disabled': cls.COLORS['text_muted'],
        }
