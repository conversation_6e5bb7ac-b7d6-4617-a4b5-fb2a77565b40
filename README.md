# NewNet File Manager

现代化的Python桌面文件管理系统，具有用户权限管理和下载控制功能。

## 功能特点

- 🔐 **安全登录** - 密码加密存储，支持管理员和普通用户
- 📁 **文件夹管理** - 添加本地文件夹到系统中进行管理
- 👥 **用户权限** - 灵活的用户权限设置，控制访问和下载权限
- 📊 **下载控制** - 设置下载次数限制，记录下载日志
- 🎨 **现代化UI** - 基于CustomTkinter的现代化界面设计
- 🔌 **API架构** - 完整的REST API，便于后期前端开发
- 💾 **SQLite存储** - 轻量级数据库，自动初始化

## 技术栈

- **GUI框架**: CustomTkinter (现代化UI组件)
- **后端API**: FastAPI
- **数据库**: SQLite3
- **密码加密**: bcrypt
- **HTTP客户端**: requests

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行程序

```bash
python main.py
```

### 3. 默认登录信息

- 用户名: `admin`
- 密码: `admin123`

**请在首次登录后修改默认密码！**

## 项目结构

```
NewNet/
├── backend/                 # 后端程序
│   ├── api/                # API接口
│   │   ├── auth.py         # 认证API
│   │   ├── folders.py      # 文件夹管理API
│   │   ├── permissions.py  # 权限管理API
│   │   └── main.py         # API服务器主程序
│   ├── database/           # 数据库操作
│   │   ├── init_db.py      # 数据库初始化
│   │   └── database.py     # 数据库管理器
│   └── models/             # 数据模型
│       └── models.py       # Pydantic模型定义
├── ui/                     # 用户界面
│   ├── login_window.py     # 登录界面
│   ├── main_window.py      # 主界面
│   └── styles.py           # UI样式定义
├── config/                 # 配置文件
│   └── settings.py         # 应用配置
├── database/               # 数据库文件存储目录
├── main.py                 # 主程序入口
└── requirements.txt        # 依赖包列表
```

## 使用说明

### 1. 登录系统
- 启动程序后会显示登录界面
- 使用默认管理员账号登录或创建新用户

### 2. 添加文件夹
- 点击"添加文件夹"按钮
- 选择要管理的本地文件夹
- 输入显示名称和描述

### 3. 管理权限
- 为不同用户设置文件夹访问权限
- 控制用户的读取和下载权限
- 设置下载次数限制

### 4. 文件操作
- 浏览文件夹中的文件
- 下载文件到本地
- 查看文件（使用系统默认程序）

## API接口

程序内置完整的REST API，运行在 `http://localhost:8000`

### 主要接口

- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/folders/` - 获取文件夹列表
- `POST /api/v1/folders/` - 添加文件夹
- `GET /api/v1/folders/{id}/files` - 获取文件列表
- `POST /api/v1/permissions/` - 创建权限
- `GET /api/v1/permissions/user/{id}` - 获取用户权限

详细API文档可访问: `http://localhost:8000/docs`

## 数据库结构

### 用户表 (users)
- id, username, password_hash, is_admin, created_at, last_login

### 文件夹表 (folders)
- id, name, path, description, is_active, created_at, created_by

### 权限表 (permissions)
- id, user_id, folder_id, can_read, can_download, download_limit, download_count

### 下载日志表 (download_logs)
- id, user_id, folder_id, file_path, download_time, ip_address

## 开发说明

### 扩展功能
- 所有业务逻辑都通过API实现，便于后期开发Web前端
- 数据库操作封装在DatabaseManager类中
- UI组件使用现代化设计风格，支持主题定制

### 安全特性
- 密码使用bcrypt加密存储
- API接口支持权限验证
- 下载操作记录完整日志

## 许可证

本项目为商业软件，版权所有。

## 联系方式

如有问题或建议，请联系开发团队。
