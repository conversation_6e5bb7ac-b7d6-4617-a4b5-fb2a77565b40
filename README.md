# NewNet File Manager

🚀 现代化的Python桌面文件管理系统，具有用户权限管理和下载控制功能。

## 🎯 项目状态

✅ **开发完成** - 所有核心功能已实现并测试通过

## 功能特点

- 🔐 **安全登录** - 密码加密存储，支持管理员和普通用户
- 📁 **文件夹管理** - 添加本地文件夹到系统中进行管理
- 👥 **用户权限** - 灵活的用户权限设置，控制访问和下载权限
- 📊 **下载控制** - 设置下载次数限制，记录下载日志
- 🎨 **现代化UI** - 基于CustomTkinter的现代化界面设计
- 🔌 **API架构** - 完整的REST API，便于后期前端开发
- 💾 **SQLite存储** - 轻量级数据库，自动初始化

## 技术栈

- **GUI框架**: CustomTkinter (现代化UI组件)
- **后端API**: FastAPI
- **数据库**: SQLite3
- **密码加密**: bcrypt
- **HTTP客户端**: requests

## 🚀 快速开始

### 方法一：一键启动（推荐）
```bash
# Windows用户
双击运行 "启动程序.bat"

# 或手动运行
python complete_main.py
```

### 方法二：简化版本（无需额外依赖）
```bash
python simple_main.py
# 或
python test_ui.py
```

### 方法三：完整版本（需要依赖）
```bash
# 1. 安装依赖
pip install customtkinter fastapi uvicorn bcrypt requests

# 2. 运行程序
python main.py
```

### 🔑 默认登录信息
- 用户名: `admin`
- 密码: `admin123`

**⚠️ 请在首次登录后修改默认密码！**

## 📁 项目结构

```
NewNet/
├── 📂 backend/                 # 后端程序统一文件夹
│   ├── 📂 api/                # REST API接口
│   │   ├── auth.py            # 🔐 认证API
│   │   ├── folders.py         # 📁 文件夹管理API
│   │   ├── permissions.py     # 👥 权限管理API
│   │   └── main.py            # 🚀 API服务器主程序
│   ├── 📂 database/           # 💾 数据库操作
│   │   ├── init_db.py         # 🔧 数据库初始化
│   │   └── database.py        # 🗄️ 数据库管理器
│   └── 📂 models/             # 📋 数据模型
│       └── models.py          # 📊 Pydantic模型定义
├── 📂 ui/                     # 🎨 用户界面
│   ├── login_window.py        # 🔑 登录界面
│   ├── main_window.py         # 🏠 主界面
│   └── styles.py              # 🎭 UI样式定义
├── 📂 config/                 # ⚙️ 配置文件
│   └── settings.py            # 📝 应用配置
├── 📂 database/               # 💾 数据库文件存储目录
├── 📄 main.py                 # 🚀 主程序入口（完整版）
├── 📄 complete_main.py        # ⭐ 完整功能版本（推荐）
├── 📄 simple_main.py          # 🔧 简化版本（仅标准库）
├── 📄 test_ui.py              # 🧪 测试版本
├── 📄 启动程序.bat            # 🚀 一键启动脚本
├── 📄 install.bat             # 📦 依赖安装脚本
└── 📄 使用说明.md             # 📖 详细使用说明
```

## 🎯 多版本说明

| 版本 | 文件名 | 特点 | 推荐度 |
|------|--------|------|--------|
| **完整版** | `complete_main.py` | 🌟 所有功能，现代UI，无需API服务器 | ⭐⭐⭐⭐⭐ |
| **简化版** | `simple_main.py` | 🔧 核心功能，标准UI，无外部依赖 | ⭐⭐⭐⭐ |
| **测试版** | `test_ui.py` | 🧪 功能测试，详细界面 | ⭐⭐⭐ |
| **API版** | `main.py` | 🚀 完整架构，需要所有依赖 | ⭐⭐ |

## 使用说明

### 1. 登录系统
- 启动程序后会显示登录界面
- 使用默认管理员账号登录或创建新用户

### 2. 添加文件夹
- 点击"添加文件夹"按钮
- 选择要管理的本地文件夹
- 输入显示名称和描述

### 3. 管理权限
- 为不同用户设置文件夹访问权限
- 控制用户的读取和下载权限
- 设置下载次数限制

### 4. 文件操作
- 浏览文件夹中的文件
- 下载文件到本地
- 查看文件（使用系统默认程序）

## API接口

程序内置完整的REST API，运行在 `http://localhost:8000`

### 主要接口

- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/folders/` - 获取文件夹列表
- `POST /api/v1/folders/` - 添加文件夹
- `GET /api/v1/folders/{id}/files` - 获取文件列表
- `POST /api/v1/permissions/` - 创建权限
- `GET /api/v1/permissions/user/{id}` - 获取用户权限

详细API文档可访问: `http://localhost:8000/docs`

## 数据库结构

### 用户表 (users)
- id, username, password_hash, is_admin, created_at, last_login

### 文件夹表 (folders)
- id, name, path, description, is_active, created_at, created_by

### 权限表 (permissions)
- id, user_id, folder_id, can_read, can_download, download_limit, download_count

### 下载日志表 (download_logs)
- id, user_id, folder_id, file_path, download_time, ip_address

## 开发说明

### 扩展功能
- 所有业务逻辑都通过API实现，便于后期开发Web前端
- 数据库操作封装在DatabaseManager类中
- UI组件使用现代化设计风格，支持主题定制

### 安全特性
- 密码使用bcrypt加密存储
- API接口支持权限验证
- 下载操作记录完整日志

## 许可证

本项目为商业软件，版权所有。

## 联系方式

如有问题或建议，请联系开发团队。
