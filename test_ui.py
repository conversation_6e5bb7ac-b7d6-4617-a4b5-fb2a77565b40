"""
测试UI界面 - 确保登录后能显示主界面
"""
import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import hashlib
from pathlib import Path
import threading
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

from config.settings import DATABASE_PATH, DATABASE_DIR

class TestApp:
    """测试应用程序"""
    
    def __init__(self):
        self.user_data = None
        self.folders_data = []
        self.setup_database()
    
    def setup_database(self):
        """设置数据库"""
        DATABASE_DIR.mkdir(exist_ok=True)
        
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 创建表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                path TEXT UNIQUE NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 检查管理员用户
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = 1")
        if cursor.fetchone()[0] == 0:
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, is_admin)
                VALUES (?, ?, ?)
            ''', ("admin", password_hash, True))
        
        conn.commit()
        conn.close()
    
    def authenticate_user(self, username, password):
        """用户认证"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('''
            SELECT id, username, is_admin FROM users 
            WHERE username = ? AND password_hash = ?
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {'id': user[0], 'username': user[1], 'is_admin': user[2]}
        return None
    
    def show_login(self):
        """显示登录界面"""
        login_window = tk.Tk()
        login_window.title("NewNet File Manager - 登录")
        login_window.geometry("400x300")
        login_window.resizable(False, False)
        
        # 居中显示
        login_window.update_idletasks()
        x = (login_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (login_window.winfo_screenheight() // 2) - (300 // 2)
        login_window.geometry(f"400x300+{x}+{y}")
        
        # 主框架
        main_frame = ttk.Frame(login_window, padding="30")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="NewNet File Manager", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 30))
        
        # 用户名
        ttk.Label(main_frame, text="用户名:", font=("Arial", 12)).pack(anchor="w")
        username_entry = ttk.Entry(main_frame, width=30, font=("Arial", 12))
        username_entry.pack(fill="x", pady=(5, 15))
        
        # 密码
        ttk.Label(main_frame, text="密码:", font=("Arial", 12)).pack(anchor="w")
        password_entry = ttk.Entry(main_frame, width=30, show="*", font=("Arial", 12))
        password_entry.pack(fill="x", pady=(5, 20))
        
        # 状态标签
        status_label = ttk.Label(main_frame, text="", foreground="red")
        status_label.pack(pady=(0, 15))
        
        def handle_login():
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            
            if not username or not password:
                status_label.config(text="请输入用户名和密码")
                return
            
            user = self.authenticate_user(username, password)
            if user:
                self.user_data = user
                status_label.config(text="登录成功！正在进入主界面...", foreground="green")
                login_window.after(1000, login_window.destroy)
            else:
                status_label.config(text="用户名或密码错误")
        
        # 登录按钮
        login_button = ttk.Button(main_frame, text="登录", command=handle_login)
        login_button.pack(fill="x", pady=(0, 15))
        
        # 提示信息
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill="x")
        
        ttk.Label(info_frame, text="默认管理员账号:", font=("Arial", 10)).pack()
        ttk.Label(info_frame, text="用户名: admin", font=("Arial", 10), foreground="blue").pack()
        ttk.Label(info_frame, text="密码: admin123", font=("Arial", 10), foreground="blue").pack()
        
        # 绑定回车键
        login_window.bind('<Return>', lambda e: handle_login())
        username_entry.focus()
        
        login_window.mainloop()
        return self.user_data is not None
    
    def load_folders(self):
        """加载文件夹列表"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT id, name, path FROM folders WHERE is_active = 1")
        self.folders_data = [{'id': row[0], 'name': row[1], 'path': row[2]} for row in cursor.fetchall()]
        conn.close()
    
    def add_folder(self):
        """添加文件夹"""
        folder_path = filedialog.askdirectory(title="选择要添加的文件夹")
        if not folder_path:
            return
        
        folder_name = os.path.basename(folder_path)
        
        try:
            conn = sqlite3.connect(DATABASE_PATH)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO folders (name, path, description)
                VALUES (?, ?, ?)
            ''', (folder_name, folder_path, f"路径: {folder_path}"))
            conn.commit()
            conn.close()
            
            messagebox.showinfo("成功", f"文件夹 '{folder_name}' 添加成功!")
            self.load_folders()
            self.update_folder_list()
            
        except sqlite3.IntegrityError:
            messagebox.showerror("错误", "该文件夹已存在!")
        except Exception as e:
            messagebox.showerror("错误", f"添加失败: {str(e)}")
    
    def show_main_window(self):
        """显示主界面"""
        main_window = tk.Tk()
        main_window.title(f"NewNet File Manager - 欢迎 {self.user_data['username']}")
        main_window.geometry("1000x700")
        
        # 居中显示
        main_window.update_idletasks()
        x = (main_window.winfo_screenwidth() // 2) - (1000 // 2)
        y = (main_window.winfo_screenheight() // 2) - (700 // 2)
        main_window.geometry(f"1000x700+{x}+{y}")
        
        # 创建菜单栏
        menubar = tk.Menu(main_window)
        main_window.config(menu=menubar)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="添加文件夹", command=self.add_folder)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=main_window.quit)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=lambda: messagebox.showinfo("关于", "NewNet File Manager v1.0.0\n现代化文件管理系统"))
        
        # 主容器
        main_container = ttk.Frame(main_window)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 顶部工具栏
        toolbar = ttk.Frame(main_container)
        toolbar.pack(fill="x", pady=(0, 10))
        
        # 工具栏按钮
        ttk.Button(toolbar, text="添加文件夹", command=self.add_folder).pack(side="left", padx=(0, 10))
        ttk.Button(toolbar, text="刷新", command=lambda: [self.load_folders(), self.update_folder_list()]).pack(side="left", padx=(0, 10))
        
        # 用户信息
        user_info = ttk.Label(toolbar, text=f"当前用户: {self.user_data['username']}" + 
                             (" (管理员)" if self.user_data['is_admin'] else ""), 
                             font=("Arial", 10))
        user_info.pack(side="right")
        
        # 主内容区域
        content_frame = ttk.Frame(main_container)
        content_frame.pack(fill="both", expand=True)
        
        # 左侧文件夹列表
        left_frame = ttk.LabelFrame(content_frame, text="文件夹列表", padding="10")
        left_frame.pack(side="left", fill="y", padx=(0, 10))
        
        # 文件夹列表框
        self.folder_listbox = tk.Listbox(left_frame, width=35, font=("Arial", 10))
        folder_scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=self.folder_listbox.yview)
        self.folder_listbox.configure(yscrollcommand=folder_scrollbar.set)
        
        self.folder_listbox.pack(side="left", fill="both", expand=True)
        folder_scrollbar.pack(side="right", fill="y")
        
        self.folder_listbox.bind('<<ListboxSelect>>', self.on_folder_select)
        
        # 右侧文件列表
        right_frame = ttk.LabelFrame(content_frame, text="文件列表", padding="10")
        right_frame.pack(side="right", fill="both", expand=True)
        
        # 文件列表树形控件
        columns = ("名称", "大小", "类型", "修改时间")
        self.file_tree = ttk.Treeview(right_frame, columns=columns, show="headings", height=20)
        
        # 设置列标题和宽度
        self.file_tree.heading("名称", text="文件名")
        self.file_tree.heading("大小", text="大小")
        self.file_tree.heading("类型", text="类型")
        self.file_tree.heading("修改时间", text="修改时间")
        
        self.file_tree.column("名称", width=300)
        self.file_tree.column("大小", width=100)
        self.file_tree.column("类型", width=80)
        self.file_tree.column("修改时间", width=150)
        
        # 滚动条
        file_scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=file_scrollbar.set)
        
        self.file_tree.pack(side="left", fill="both", expand=True)
        file_scrollbar.pack(side="right", fill="y")
        
        # 双击打开文件
        self.file_tree.bind('<Double-1>', self.open_file)
        
        # 右键菜单
        context_menu = tk.Menu(main_window, tearoff=0)
        context_menu.add_command(label="打开", command=self.open_selected_file)
        context_menu.add_command(label="复制路径", command=self.copy_file_path)
        
        def show_context_menu(event):
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        self.file_tree.bind("<Button-3>", show_context_menu)
        
        # 状态栏
        status_frame = ttk.Frame(main_container)
        status_frame.pack(fill="x", pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="就绪", relief="sunken", anchor="w")
        self.status_label.pack(fill="x")
        
        # 加载数据
        self.load_folders()
        self.update_folder_list()
        
        # 显示欢迎信息
        self.status_label.config(text=f"欢迎使用 NewNet File Manager, {self.user_data['username']}!")
        
        main_window.mainloop()
    
    def update_folder_list(self):
        """更新文件夹列表显示"""
        self.folder_listbox.delete(0, tk.END)
        for folder in self.folders_data:
            self.folder_listbox.insert(tk.END, f"{folder['name']} ({folder['path']})")
    
    def on_folder_select(self, event):
        """文件夹选择事件"""
        selection = self.folder_listbox.curselection()
        if not selection:
            return
        
        folder_index = selection[0]
        folder = self.folders_data[folder_index]
        self.load_files(folder['path'])
        self.status_label.config(text=f"已选择文件夹: {folder['name']}")
    
    def load_files(self, folder_path):
        """加载文件列表"""
        # 清空文件列表
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
        
        try:
            folder = Path(folder_path)
            if not folder.exists():
                messagebox.showerror("错误", f"文件夹不存在: {folder_path}")
                return
            
            file_count = 0
            for file_path in folder.iterdir():
                if file_path.is_file():
                    name = file_path.name
                    size = self.format_file_size(file_path.stat().st_size)
                    file_type = file_path.suffix.upper() if file_path.suffix else "文件"
                    modified = time.strftime("%Y-%m-%d %H:%M", time.localtime(file_path.stat().st_mtime))
                    
                    self.file_tree.insert("", tk.END, values=(name, size, file_type, modified),
                                        tags=(str(file_path),))
                    file_count += 1
            
            self.status_label.config(text=f"共找到 {file_count} 个文件")
        
        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)}")
            self.status_label.config(text="加载文件失败")
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def open_file(self, event):
        """双击打开文件"""
        self.open_selected_file()
    
    def open_selected_file(self):
        """打开选中的文件"""
        selection = self.file_tree.selection()
        if not selection:
            return
        
        item = self.file_tree.item(selection[0])
        file_path = item['tags'][0]
        
        try:
            os.startfile(file_path)
            self.status_label.config(text=f"已打开文件: {os.path.basename(file_path)}")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件: {str(e)}")
    
    def copy_file_path(self):
        """复制文件路径到剪贴板"""
        selection = self.file_tree.selection()
        if not selection:
            return
        
        item = self.file_tree.item(selection[0])
        file_path = item['tags'][0]
        
        try:
            # 复制到剪贴板
            import tkinter.messagebox
            self.file_tree.clipboard_clear()
            self.file_tree.clipboard_append(file_path)
            self.status_label.config(text=f"已复制路径: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")
    
    def run(self):
        """运行应用程序"""
        print("=" * 60)
        print("NewNet File Manager - 测试版本")
        print("=" * 60)
        print("功能说明:")
        print("1. 登录系统 (默认: admin/admin123)")
        print("2. 添加文件夹到系统管理")
        print("3. 浏览文件夹中的文件")
        print("4. 双击打开文件")
        print("5. 右键菜单操作")
        print("=" * 60)
        
        if self.show_login():
            print(f"登录成功: {self.user_data['username']} ({'管理员' if self.user_data['is_admin'] else '普通用户'})")
            self.show_main_window()
        else:
            print("登录取消或失败")

if __name__ == "__main__":
    app = TestApp()
    app.run()
