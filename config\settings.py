"""
应用程序配置文件
"""
import os
from pathlib import Path

# 应用程序基本配置
APP_NAME = "NewNet File Manager"
APP_VERSION = "1.0.0"

# 路径配置
BASE_DIR = Path(__file__).parent.parent
DATABASE_DIR = BASE_DIR / "database"
DATABASE_PATH = DATABASE_DIR / "newnet.db"

# API配置
API_HOST = "127.0.0.1"
API_PORT = 8000
API_PREFIX = "/api/v1"

# UI配置
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
WINDOW_MIN_WIDTH = 800
WINDOW_MIN_HEIGHT = 600

# 主题配置
THEME_MODE = "light"  # light/dark
THEME_COLOR = "blue"  # blue/green/dark-blue

# 安全配置
SECRET_KEY = "your-secret-key-change-in-production"
PASSWORD_HASH_ROUNDS = 12

# 文件管理配置
MAX_FOLDER_DEPTH = 10
ALLOWED_FILE_EXTENSIONS = [
    '.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', 
    '.ppt', '.pptx', '.jpg', '.jpeg', '.png', '.gif',
    '.mp4', '.avi', '.mp3', '.wav', '.zip', '.rar'
]

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    DATABASE_DIR.mkdir(exist_ok=True)

if __name__ == "__main__":
    ensure_directories()
