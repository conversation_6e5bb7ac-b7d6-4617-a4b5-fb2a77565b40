@echo off
chcp 65001 >nul
echo ================================================
echo NewNet File Manager v1.0.0
echo 现代化文件管理系统
echo ================================================
echo.

echo 检查运行环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🚀 启动程序...
echo.
echo 💡 提示: 
echo    默认登录账号: admin
echo    默认登录密码: admin123
echo.

REM 尝试启动完整版本
python complete_main.py

if errorlevel 1 (
    echo.
    echo ⚠️  完整版本启动失败，尝试简化版本...
    python simple_main.py
    
    if errorlevel 1 (
        echo.
        echo ❌ 程序启动失败
        echo 💡 建议:
        echo    1. 检查Python版本是否为3.7+
        echo    2. 尝试安装依赖: pip install customtkinter
        echo    3. 或直接运行: python test_ui.py
        pause
    )
)

echo.
echo 程序已退出
pause
