"""
认证API接口
"""
from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from backend.models.models import UserLogin, UserCreate, LoginResponse, ApiResponse
from backend.database.database import DatabaseManager

router = APIRouter(prefix="/auth", tags=["认证"])
db_manager = DatabaseManager()

@router.post("/login", response_model=LoginResponse)
async def login(user_data: UserLogin):
    """用户登录"""
    try:
        user = db_manager.authenticate_user(user_data.username, user_data.password)
        
        if user:
            return LoginResponse(
                success=True,
                message="登录成功",
                user_id=user['id'],
                username=user['username'],
                is_admin=user['is_admin']
            )
        else:
            return LoginResponse(
                success=False,
                message="用户名或密码错误"
            )
    except Exception as e:
        return LoginResponse(
            success=False,
            message=f"登录失败: {str(e)}"
        )

@router.post("/register", response_model=ApiResponse)
async def register(user_data: UserCreate):
    """用户注册"""
    try:
        success = db_manager.create_user(
            username=user_data.username,
            password=user_data.password,
            is_admin=user_data.is_admin
        )
        
        if success:
            return ApiResponse(
                success=True,
                message="用户创建成功"
            )
        else:
            return ApiResponse(
                success=False,
                message="用户名已存在"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"用户创建失败: {str(e)}"
        )

@router.get("/users", response_model=ApiResponse)
async def get_users():
    """获取所有用户（管理员功能）"""
    try:
        users = db_manager.get_all_users()
        return ApiResponse(
            success=True,
            message="获取用户列表成功",
            data={"users": users}
        )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"获取用户列表失败: {str(e)}"
        )

@router.get("/user/{user_id}", response_model=ApiResponse)
async def get_user(user_id: int):
    """获取用户信息"""
    try:
        user = db_manager.get_user_by_id(user_id)
        if user:
            return ApiResponse(
                success=True,
                message="获取用户信息成功",
                data={"user": user}
            )
        else:
            return ApiResponse(
                success=False,
                message="用户不存在"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"获取用户信息失败: {str(e)}"
        )
