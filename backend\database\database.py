"""
数据库操作模块
"""
import sqlite3
import bcrypt
from datetime import datetime
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from config.settings import DATABASE_PATH
from backend.models.models import User, Folder, Permission, DownloadLog

class DatabaseManager:
    def __init__(self):
        self.db_path = DATABASE_PATH
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    # 用户相关操作
    def create_user(self, username: str, password: str, is_admin: bool = False) -> bool:
        """创建用户"""
        try:
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO users (username, password_hash, is_admin)
                    VALUES (?, ?, ?)
                ''', (username, password_hash.decode('utf-8'), is_admin))
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False  # 用户名已存在
        except Exception as e:
            print(f"创建用户失败: {e}")
            return False
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """用户认证"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, username, password_hash, is_admin
                    FROM users WHERE username = ?
                ''', (username,))
                
                user = cursor.fetchone()
                if user and bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                    # 更新最后登录时间
                    cursor.execute('''
                        UPDATE users SET last_login = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (user['id'],))
                    conn.commit()
                    
                    return {
                        'id': user['id'],
                        'username': user['username'],
                        'is_admin': user['is_admin']
                    }
                return None
        except Exception as e:
            print(f"用户认证失败: {e}")
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取用户"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, username, is_admin, created_at, last_login
                    FROM users WHERE id = ?
                ''', (user_id,))
                
                user = cursor.fetchone()
                if user:
                    return dict(user)
                return None
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """获取所有用户"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, username, is_admin, created_at, last_login
                    FROM users ORDER BY created_at DESC
                ''')
                
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取用户列表失败: {e}")
            return []
    
    # 文件夹相关操作
    def create_folder(self, name: str, path: str, description: str = None, created_by: int = None) -> Optional[int]:
        """创建文件夹记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO folders (name, path, description, created_by)
                    VALUES (?, ?, ?, ?)
                ''', (name, path, description, created_by))
                conn.commit()
                return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None  # 路径已存在
        except Exception as e:
            print(f"创建文件夹记录失败: {e}")
            return None
    
    def get_folder_by_id(self, folder_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取文件夹"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM folders WHERE id = ?
                ''', (folder_id,))
                
                folder = cursor.fetchone()
                if folder:
                    return dict(folder)
                return None
        except Exception as e:
            print(f"获取文件夹失败: {e}")
            return None
    
    def get_all_folders(self) -> List[Dict[str, Any]]:
        """获取所有文件夹"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT f.*, u.username as created_by_name
                    FROM folders f
                    LEFT JOIN users u ON f.created_by = u.id
                    WHERE f.is_active = TRUE
                    ORDER BY f.created_at DESC
                ''')
                
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取文件夹列表失败: {e}")
            return []

    def update_folder(self, folder_id: int, name: str = None, description: str = None, is_active: bool = None) -> bool:
        """更新文件夹信息"""
        try:
            updates = []
            params = []

            if name is not None:
                updates.append("name = ?")
                params.append(name)
            if description is not None:
                updates.append("description = ?")
                params.append(description)
            if is_active is not None:
                updates.append("is_active = ?")
                params.append(is_active)

            if not updates:
                return True

            params.append(folder_id)

            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    UPDATE folders SET {", ".join(updates)}
                    WHERE id = ?
                ''', params)
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新文件夹失败: {e}")
            return False

    # 权限相关操作
    def create_permission(self, user_id: int, folder_id: int, can_read: bool = True,
                         can_download: bool = False, download_limit: int = None) -> bool:
        """创建权限"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO permissions (user_id, folder_id, can_read, can_download, download_limit)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, folder_id, can_read, can_download, download_limit))
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False  # 权限已存在
        except Exception as e:
            print(f"创建权限失败: {e}")
            return False

    def get_user_permissions(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户权限"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT p.*, f.name as folder_name, f.path as folder_path
                    FROM permissions p
                    JOIN folders f ON p.folder_id = f.id
                    WHERE p.user_id = ? AND f.is_active = TRUE
                    ORDER BY f.name
                ''', (user_id,))

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取用户权限失败: {e}")
            return []

    def get_folder_permissions(self, folder_id: int) -> List[Dict[str, Any]]:
        """获取文件夹权限"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT p.*, u.username
                    FROM permissions p
                    JOIN users u ON p.user_id = u.id
                    WHERE p.folder_id = ?
                    ORDER BY u.username
                ''', (folder_id,))

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取文件夹权限失败: {e}")
            return []

    def update_permission(self, user_id: int, folder_id: int, can_read: bool = None,
                         can_download: bool = None, download_limit: int = None) -> bool:
        """更新权限"""
        try:
            updates = []
            params = []

            if can_read is not None:
                updates.append("can_read = ?")
                params.append(can_read)
            if can_download is not None:
                updates.append("can_download = ?")
                params.append(can_download)
            if download_limit is not None:
                updates.append("download_limit = ?")
                params.append(download_limit)

            if not updates:
                return True

            params.extend([user_id, folder_id])

            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    UPDATE permissions SET {", ".join(updates)}
                    WHERE user_id = ? AND folder_id = ?
                ''', params)
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新权限失败: {e}")
            return False

    def increment_download_count(self, user_id: int, folder_id: int) -> bool:
        """增加下载次数"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE permissions SET download_count = download_count + 1
                    WHERE user_id = ? AND folder_id = ?
                ''', (user_id, folder_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新下载次数失败: {e}")
            return False

    # 下载日志相关操作
    def create_download_log(self, user_id: int, folder_id: int, file_path: str, ip_address: str = None) -> bool:
        """创建下载日志"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO download_logs (user_id, folder_id, file_path, ip_address)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, folder_id, file_path, ip_address))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建下载日志失败: {e}")
            return False

    def get_download_logs(self, user_id: int = None, folder_id: int = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取下载日志"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT dl.*, u.username, f.name as folder_name
                    FROM download_logs dl
                    JOIN users u ON dl.user_id = u.id
                    JOIN folders f ON dl.folder_id = f.id
                '''
                params = []

                conditions = []
                if user_id:
                    conditions.append("dl.user_id = ?")
                    params.append(user_id)
                if folder_id:
                    conditions.append("dl.folder_id = ?")
                    params.append(folder_id)

                if conditions:
                    query += " WHERE " + " AND ".join(conditions)

                query += " ORDER BY dl.download_time DESC LIMIT ?"
                params.append(limit)

                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取下载日志失败: {e}")
            return []
