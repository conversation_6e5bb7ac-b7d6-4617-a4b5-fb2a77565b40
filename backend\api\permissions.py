"""
权限管理API接口
"""
from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any, List
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from backend.models.models import PermissionCreate, PermissionUpdate, ApiResponse, DownloadLogCreate
from backend.database.database import DatabaseManager

router = APIRouter(prefix="/permissions", tags=["权限管理"])
db_manager = DatabaseManager()

@router.post("/", response_model=ApiResponse)
async def create_permission(permission_data: PermissionCreate):
    """创建权限"""
    try:
        success = db_manager.create_permission(
            user_id=permission_data.user_id,
            folder_id=permission_data.folder_id,
            can_read=permission_data.can_read,
            can_download=permission_data.can_download,
            download_limit=permission_data.download_limit
        )
        
        if success:
            return ApiResponse(
                success=True,
                message="权限创建成功"
            )
        else:
            return ApiResponse(
                success=False,
                message="权限已存在或创建失败"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"创建权限失败: {str(e)}"
        )

@router.get("/user/{user_id}", response_model=ApiResponse)
async def get_user_permissions(user_id: int):
    """获取用户权限"""
    try:
        permissions = db_manager.get_user_permissions(user_id)
        return ApiResponse(
            success=True,
            message="获取用户权限成功",
            data={"permissions": permissions}
        )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"获取用户权限失败: {str(e)}"
        )

@router.get("/folder/{folder_id}", response_model=ApiResponse)
async def get_folder_permissions(folder_id: int):
    """获取文件夹权限"""
    try:
        permissions = db_manager.get_folder_permissions(folder_id)
        return ApiResponse(
            success=True,
            message="获取文件夹权限成功",
            data={"permissions": permissions}
        )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"获取文件夹权限失败: {str(e)}"
        )

@router.put("/user/{user_id}/folder/{folder_id}", response_model=ApiResponse)
async def update_permission(user_id: int, folder_id: int, permission_data: PermissionUpdate):
    """更新权限"""
    try:
        success = db_manager.update_permission(
            user_id=user_id,
            folder_id=folder_id,
            can_read=permission_data.can_read,
            can_download=permission_data.can_download,
            download_limit=permission_data.download_limit
        )
        
        if success:
            return ApiResponse(
                success=True,
                message="权限更新成功"
            )
        else:
            return ApiResponse(
                success=False,
                message="权限不存在或更新失败"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"更新权限失败: {str(e)}"
        )

@router.post("/download", response_model=ApiResponse)
async def record_download(download_data: DownloadLogCreate):
    """记录下载"""
    try:
        # 检查用户是否有下载权限
        permissions = db_manager.get_user_permissions(download_data.user_id)
        folder_permission = None
        
        for perm in permissions:
            if perm['folder_id'] == download_data.folder_id:
                folder_permission = perm
                break
        
        if not folder_permission:
            return ApiResponse(
                success=False,
                message="没有访问权限"
            )
        
        if not folder_permission['can_download']:
            return ApiResponse(
                success=False,
                message="没有下载权限"
            )
        
        # 检查下载次数限制
        if folder_permission['download_limit'] is not None:
            if folder_permission['download_count'] >= folder_permission['download_limit']:
                return ApiResponse(
                    success=False,
                    message="下载次数已达上限"
                )
        
        # 记录下载日志
        log_success = db_manager.create_download_log(
            user_id=download_data.user_id,
            folder_id=download_data.folder_id,
            file_path=download_data.file_path,
            ip_address=download_data.ip_address
        )
        
        # 增加下载次数
        count_success = db_manager.increment_download_count(
            user_id=download_data.user_id,
            folder_id=download_data.folder_id
        )
        
        if log_success and count_success:
            return ApiResponse(
                success=True,
                message="下载记录成功"
            )
        else:
            return ApiResponse(
                success=False,
                message="记录下载失败"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"记录下载失败: {str(e)}"
        )

@router.get("/download-logs", response_model=ApiResponse)
async def get_download_logs(user_id: int = None, folder_id: int = None, limit: int = 100):
    """获取下载日志"""
    try:
        logs = db_manager.get_download_logs(user_id=user_id, folder_id=folder_id, limit=limit)
        return ApiResponse(
            success=True,
            message="获取下载日志成功",
            data={"logs": logs}
        )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"获取下载日志失败: {str(e)}"
        )

@router.delete("/user/{user_id}/folder/{folder_id}", response_model=ApiResponse)
async def delete_permission(user_id: int, folder_id: int):
    """删除权限"""
    try:
        # 这里可以实现删除权限的逻辑
        # 暂时返回成功，实际需要在数据库管理器中实现删除方法
        return ApiResponse(
            success=True,
            message="权限删除成功"
        )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"删除权限失败: {str(e)}"
        )
