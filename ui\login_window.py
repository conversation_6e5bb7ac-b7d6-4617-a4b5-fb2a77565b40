"""
登录窗口界面
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import requests
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from ui.styles import ModernStyles
from config.settings import API_HOST, API_PORT, API_PREFIX

class LoginWindow:
    """登录窗口类"""
    
    def __init__(self):
        self.window = None
        self.username_entry = None
        self.password_entry = None
        self.login_callback = None
        self.user_data = None
        
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """设置窗口"""
        self.window = ctk.CTk()
        self.window.title("NewNet File Manager - 登录")
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        
        # 居中显示
        self.center_window()
        
        # 设置窗口图标（如果有的话）
        try:
            # self.window.iconbitmap("icon.ico")
            pass
        except:
            pass
    
    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ctk.CTkFrame(
            self.window,
            **ModernStyles.get_frame_style('card')
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题区域
        title_frame = ctk.CTkFrame(
            main_frame,
            fg_color="transparent"
        )
        title_frame.pack(fill="x", padx=30, pady=(30, 20))
        
        # 应用标题
        title_label = ctk.CTkLabel(
            title_frame,
            text="NewNet",
            **ModernStyles.get_label_style('title')
        )
        title_label.pack()
        
        # 副标题
        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="文件管理系统",
            **ModernStyles.get_label_style('secondary')
        )
        subtitle_label.pack(pady=(5, 0))
        
        # 登录表单区域
        form_frame = ctk.CTkFrame(
            main_frame,
            fg_color="transparent"
        )
        form_frame.pack(fill="x", padx=30, pady=20)
        
        # 用户名输入
        username_label = ctk.CTkLabel(
            form_frame,
            text="用户名",
            **ModernStyles.get_label_style('body')
        )
        username_label.pack(anchor="w", pady=(0, 5))
        
        self.username_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="请输入用户名",
            **ModernStyles.get_entry_style()
        )
        self.username_entry.pack(fill="x", pady=(0, 15))
        
        # 密码输入
        password_label = ctk.CTkLabel(
            form_frame,
            text="密码",
            **ModernStyles.get_label_style('body')
        )
        password_label.pack(anchor="w", pady=(0, 5))
        
        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="请输入密码",
            show="*",
            **ModernStyles.get_entry_style()
        )
        self.password_entry.pack(fill="x", pady=(0, 25))
        
        # 登录按钮
        login_button = ctk.CTkButton(
            form_frame,
            text="登录",
            command=self.handle_login,
            **ModernStyles.get_button_style('primary')
        )
        login_button.pack(fill="x", pady=(0, 15))
        
        # 状态信息
        self.status_label = ctk.CTkLabel(
            form_frame,
            text="",
            **ModernStyles.get_label_style('muted')
        )
        self.status_label.pack()
        
        # 底部信息
        footer_frame = ctk.CTkFrame(
            main_frame,
            fg_color="transparent"
        )
        footer_frame.pack(side="bottom", fill="x", padx=30, pady=(0, 30))
        
        footer_label = ctk.CTkLabel(
            footer_frame,
            text="默认管理员账号: admin / admin123",
            **ModernStyles.get_label_style('muted')
        )
        footer_label.pack()
        
        # 绑定回车键
        self.window.bind('<Return>', lambda event: self.handle_login())
        self.username_entry.bind('<Return>', lambda event: self.password_entry.focus())
        self.password_entry.bind('<Return>', lambda event: self.handle_login())
        
        # 设置初始焦点
        self.username_entry.focus()
    
    def handle_login(self):
        """处理登录"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            self.show_status("请输入用户名和密码", "error")
            return
        
        self.show_status("正在登录...", "info")
        
        try:
            # 调用API进行登录
            response = requests.post(
                f"http://{API_HOST}:{API_PORT}{API_PREFIX}/auth/login",
                json={
                    "username": username,
                    "password": password
                },
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.user_data = {
                        'user_id': data.get('user_id'),
                        'username': data.get('username'),
                        'is_admin': data.get('is_admin')
                    }
                    self.show_status("登录成功!", "success")
                    
                    # 延迟关闭窗口
                    self.window.after(1000, self.close_window)
                else:
                    self.show_status(data.get('message', '登录失败'), "error")
            else:
                self.show_status("服务器连接失败", "error")
                
        except requests.exceptions.ConnectionError:
            self.show_status("无法连接到服务器", "error")
        except requests.exceptions.Timeout:
            self.show_status("连接超时", "error")
        except Exception as e:
            self.show_status(f"登录失败: {str(e)}", "error")
    
    def show_status(self, message, status_type="info"):
        """显示状态信息"""
        colors = {
            "info": ModernStyles.COLORS['info'],
            "success": ModernStyles.COLORS['success'],
            "error": ModernStyles.COLORS['error'],
            "warning": ModernStyles.COLORS['warning']
        }
        
        self.status_label.configure(
            text=message,
            text_color=colors.get(status_type, ModernStyles.COLORS['text_muted'])
        )
    
    def close_window(self):
        """关闭窗口"""
        if self.login_callback and self.user_data:
            self.login_callback(self.user_data)
        self.window.destroy()
    
    def set_login_callback(self, callback):
        """设置登录成功回调"""
        self.login_callback = callback
    
    def show(self):
        """显示窗口"""
        self.window.mainloop()
        return self.user_data

if __name__ == "__main__":
    # 测试登录窗口
    login = LoginWindow()
    user_data = login.show()
    if user_data:
        print(f"登录成功: {user_data}")
    else:
        print("登录取消或失败")
