@echo off
echo ================================================
echo NewNet File Manager - 依赖安装脚本
echo ================================================
echo.

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 安装依赖包...
echo 正在安装 customtkinter...
python -m pip install customtkinter

echo 正在安装 fastapi...
python -m pip install fastapi

echo 正在安装 uvicorn...
python -m pip install uvicorn

echo 正在安装 bcrypt...
python -m pip install bcrypt

echo 正在安装 requests...
python -m pip install requests

echo 正在安装 pillow...
python -m pip install pillow

echo 正在安装 python-multipart...
python -m pip install python-multipart

echo 正在安装 pydantic...
python -m pip install pydantic

echo.
echo ================================================
echo 安装完成！
echo ================================================
echo.
echo 现在可以运行程序了:
echo   完整版本: python main.py
echo   简化版本: python simple_main.py
echo.
pause
