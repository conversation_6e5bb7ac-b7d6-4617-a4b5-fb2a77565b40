"""
NewNet File Manager - 简化版本（仅使用标准库）
用于测试基本功能
"""
import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import hashlib
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

from config.settings import DATABASE_PATH, DATABASE_DIR

class SimpleApp:
    """简化版应用程序"""
    
    def __init__(self):
        self.root = None
        self.user_data = None
        self.setup_database()
    
    def setup_database(self):
        """设置数据库"""
        # 确保数据库目录存在
        DATABASE_DIR.mkdir(exist_ok=True)
        
        # 创建数据库连接
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 创建用户表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建文件夹表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                path TEXT UNIQUE NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 检查是否有管理员用户
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = 1")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            # 创建默认管理员
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, is_admin)
                VALUES (?, ?, ?)
            ''', ("admin", password_hash, True))
            print("默认管理员用户已创建: admin / admin123")
        
        conn.commit()
        conn.close()
    
    def authenticate_user(self, username, password):
        """用户认证"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('''
            SELECT id, username, is_admin FROM users 
            WHERE username = ? AND password_hash = ?
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                'id': user[0],
                'username': user[1],
                'is_admin': user[2]
            }
        return None
    
    def show_login(self):
        """显示登录界面"""
        login_window = tk.Tk()
        login_window.title("NewNet File Manager - 登录")
        login_window.geometry("350x250")
        login_window.resizable(False, False)
        
        # 居中显示
        login_window.update_idletasks()
        x = (login_window.winfo_screenwidth() // 2) - (350 // 2)
        y = (login_window.winfo_screenheight() // 2) - (250 // 2)
        login_window.geometry(f"350x250+{x}+{y}")
        
        # 创建界面
        main_frame = ttk.Frame(login_window, padding="20")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="NewNet File Manager", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 用户名
        ttk.Label(main_frame, text="用户名:").pack(anchor="w")
        username_entry = ttk.Entry(main_frame, width=30)
        username_entry.pack(fill="x", pady=(5, 10))
        
        # 密码
        ttk.Label(main_frame, text="密码:").pack(anchor="w")
        password_entry = ttk.Entry(main_frame, width=30, show="*")
        password_entry.pack(fill="x", pady=(5, 15))
        
        # 状态标签
        status_label = ttk.Label(main_frame, text="", foreground="red")
        status_label.pack(pady=(0, 10))
        
        def handle_login():
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            
            if not username or not password:
                status_label.config(text="请输入用户名和密码")
                return
            
            user = self.authenticate_user(username, password)
            if user:
                self.user_data = user
                login_window.destroy()
            else:
                status_label.config(text="用户名或密码错误")
        
        # 登录按钮
        login_button = ttk.Button(main_frame, text="登录", command=handle_login)
        login_button.pack(fill="x", pady=(0, 10))
        
        # 提示信息
        ttk.Label(main_frame, text="默认账号: admin / admin123", 
                 font=("Arial", 8), foreground="gray").pack()
        
        # 绑定回车键
        login_window.bind('<Return>', lambda e: handle_login())
        username_entry.focus()
        
        login_window.mainloop()
        return self.user_data is not None
    
    def show_main_window(self):
        """显示主界面"""
        self.root = tk.Tk()
        self.root.title(f"NewNet File Manager - {self.user_data['username']}")
        self.root.geometry("800x600")
        
        # 创建菜单
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="添加文件夹", command=self.add_folder)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 左侧文件夹列表
        left_frame = ttk.LabelFrame(main_frame, text="文件夹列表", padding="5")
        left_frame.pack(side="left", fill="y", padx=(0, 5))
        
        # 文件夹列表框
        self.folder_listbox = tk.Listbox(left_frame, width=30)
        self.folder_listbox.pack(fill="both", expand=True)
        self.folder_listbox.bind('<<ListboxSelect>>', self.on_folder_select)
        
        # 右侧文件列表
        right_frame = ttk.LabelFrame(main_frame, text="文件列表", padding="5")
        right_frame.pack(side="right", fill="both", expand=True)
        
        # 文件列表
        columns = ("名称", "大小", "类型")
        self.file_tree = ttk.Treeview(right_frame, columns=columns, show="headings")
        
        for col in columns:
            self.file_tree.heading(col, text=col)
            self.file_tree.column(col, width=150)
        
        self.file_tree.pack(fill="both", expand=True)
        
        # 双击打开文件
        self.file_tree.bind('<Double-1>', self.open_file)
        
        # 加载文件夹列表
        self.load_folders()
        
        self.root.mainloop()
    
    def add_folder(self):
        """添加文件夹"""
        folder_path = filedialog.askdirectory(title="选择文件夹")
        if not folder_path:
            return
        
        folder_name = os.path.basename(folder_path)
        
        try:
            conn = sqlite3.connect(DATABASE_PATH)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO folders (name, path, description)
                VALUES (?, ?, ?)
            ''', (folder_name, folder_path, f"路径: {folder_path}"))
            conn.commit()
            conn.close()
            
            messagebox.showinfo("成功", "文件夹添加成功!")
            self.load_folders()
            
        except sqlite3.IntegrityError:
            messagebox.showerror("错误", "该文件夹已存在!")
        except Exception as e:
            messagebox.showerror("错误", f"添加失败: {str(e)}")
    
    def load_folders(self):
        """加载文件夹列表"""
        self.folder_listbox.delete(0, tk.END)
        
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT id, name, path FROM folders WHERE is_active = 1")
        folders = cursor.fetchall()
        conn.close()
        
        self.folders_data = {}
        for folder_id, name, path in folders:
            self.folder_listbox.insert(tk.END, name)
            self.folders_data[name] = {'id': folder_id, 'path': path}
    
    def on_folder_select(self, event):
        """文件夹选择事件"""
        selection = self.folder_listbox.curselection()
        if not selection:
            return
        
        folder_name = self.folder_listbox.get(selection[0])
        folder_path = self.folders_data[folder_name]['path']
        
        self.load_files(folder_path)
    
    def load_files(self, folder_path):
        """加载文件列表"""
        # 清空文件列表
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
        
        try:
            folder = Path(folder_path)
            if not folder.exists():
                messagebox.showerror("错误", "文件夹不存在!")
                return
            
            for file_path in folder.iterdir():
                if file_path.is_file():
                    name = file_path.name
                    size = self.format_file_size(file_path.stat().st_size)
                    file_type = file_path.suffix.upper() if file_path.suffix else "文件"
                    
                    self.file_tree.insert("", tk.END, values=(name, size, file_type),
                                        tags=(str(file_path),))
        
        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)}")
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def open_file(self, event):
        """打开文件"""
        selection = self.file_tree.selection()
        if not selection:
            return
        
        item = self.file_tree.item(selection[0])
        file_path = item['tags'][0]
        
        try:
            os.startfile(file_path)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件: {str(e)}")
    
    def run(self):
        """运行应用程序"""
        print("=" * 50)
        print("NewNet File Manager - 简化版")
        print("=" * 50)
        
        if self.show_login():
            print(f"登录成功: {self.user_data['username']}")
            self.show_main_window()
        else:
            print("登录取消")

if __name__ == "__main__":
    app = SimpleApp()
    app.run()
