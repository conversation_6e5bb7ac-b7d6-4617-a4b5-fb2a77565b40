@echo off
echo ================================================
echo NewNet File Manager v1.0.0
echo ================================================
echo.

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo.
echo 检查依赖包...
python -c "import tkinter; print('tkinter: OK')" 2>nul || echo "tkinter: 缺失"
python -c "import sqlite3; print('sqlite3: OK')" 2>nul || echo "sqlite3: 缺失"
python -c "import customtkinter; print('customtkinter: OK')" 2>nul || echo "customtkinter: 需要安装"
python -c "import requests; print('requests: OK')" 2>nul || echo "requests: 需要安装"
python -c "import bcrypt; print('bcrypt: OK')" 2>nul || echo "bcrypt: 需要安装"
python -c "import fastapi; print('fastapi: OK')" 2>nul || echo "fastapi: 需要安装"

echo.
echo 如果有依赖包缺失，请运行以下命令安装:
echo pip install customtkinter requests bcrypt fastapi uvicorn pillow python-multipart pydantic
echo.

echo 启动程序...
python main.py

echo.
echo 程序已退出
pause
