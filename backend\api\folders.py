"""
文件夹管理API接口
"""
from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any, List
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from backend.models.models import FolderCreate, FolderUpdate, ApiResponse
from backend.database.database import DatabaseManager

router = APIRouter(prefix="/folders", tags=["文件夹管理"])
db_manager = DatabaseManager()

@router.post("/", response_model=ApiResponse)
async def create_folder(folder_data: FolderCreate, created_by: int = None):
    """添加文件夹"""
    try:
        # 验证文件夹路径是否存在
        folder_path = Path(folder_data.path)
        if not folder_path.exists():
            return ApiResponse(
                success=False,
                message="指定的文件夹路径不存在"
            )
        
        if not folder_path.is_dir():
            return ApiResponse(
                success=False,
                message="指定的路径不是文件夹"
            )
        
        # 创建文件夹记录
        folder_id = db_manager.create_folder(
            name=folder_data.name,
            path=str(folder_path.absolute()),
            description=folder_data.description,
            created_by=created_by
        )
        
        if folder_id:
            return ApiResponse(
                success=True,
                message="文件夹添加成功",
                data={"folder_id": folder_id}
            )
        else:
            return ApiResponse(
                success=False,
                message="文件夹路径已存在"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"添加文件夹失败: {str(e)}"
        )

@router.get("/", response_model=ApiResponse)
async def get_folders():
    """获取所有文件夹"""
    try:
        folders = db_manager.get_all_folders()
        return ApiResponse(
            success=True,
            message="获取文件夹列表成功",
            data={"folders": folders}
        )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"获取文件夹列表失败: {str(e)}"
        )

@router.get("/{folder_id}", response_model=ApiResponse)
async def get_folder(folder_id: int):
    """获取文件夹详情"""
    try:
        folder = db_manager.get_folder_by_id(folder_id)
        if folder:
            return ApiResponse(
                success=True,
                message="获取文件夹信息成功",
                data={"folder": folder}
            )
        else:
            return ApiResponse(
                success=False,
                message="文件夹不存在"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"获取文件夹信息失败: {str(e)}"
        )

@router.put("/{folder_id}", response_model=ApiResponse)
async def update_folder(folder_id: int, folder_data: FolderUpdate):
    """更新文件夹信息"""
    try:
        success = db_manager.update_folder(
            folder_id=folder_id,
            name=folder_data.name,
            description=folder_data.description,
            is_active=folder_data.is_active
        )
        
        if success:
            return ApiResponse(
                success=True,
                message="文件夹更新成功"
            )
        else:
            return ApiResponse(
                success=False,
                message="文件夹不存在或更新失败"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"更新文件夹失败: {str(e)}"
        )

@router.get("/{folder_id}/files", response_model=ApiResponse)
async def get_folder_files(folder_id: int):
    """获取文件夹中的文件列表"""
    try:
        folder = db_manager.get_folder_by_id(folder_id)
        if not folder:
            return ApiResponse(
                success=False,
                message="文件夹不存在"
            )
        
        folder_path = Path(folder['path'])
        if not folder_path.exists():
            return ApiResponse(
                success=False,
                message="文件夹路径不存在"
            )
        
        files = []
        for item in folder_path.iterdir():
            if item.is_file():
                files.append({
                    "name": item.name,
                    "path": str(item),
                    "size": item.stat().st_size,
                    "modified": item.stat().st_mtime,
                    "extension": item.suffix.lower()
                })
        
        # 按名称排序
        files.sort(key=lambda x: x['name'].lower())
        
        return ApiResponse(
            success=True,
            message="获取文件列表成功",
            data={
                "folder": folder,
                "files": files,
                "file_count": len(files)
            }
        )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"获取文件列表失败: {str(e)}"
        )

@router.delete("/{folder_id}", response_model=ApiResponse)
async def delete_folder(folder_id: int):
    """删除文件夹（软删除）"""
    try:
        success = db_manager.update_folder(
            folder_id=folder_id,
            is_active=False
        )
        
        if success:
            return ApiResponse(
                success=True,
                message="文件夹删除成功"
            )
        else:
            return ApiResponse(
                success=False,
                message="文件夹不存在或删除失败"
            )
    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"删除文件夹失败: {str(e)}"
        )
