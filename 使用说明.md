# NewNet File Manager 使用说明

## 快速开始

### 1. 环境要求
- Windows 10/11
- Python 3.7 或更高版本

### 2. 安装步骤

#### 方法一：自动安装（推荐）
1. 双击运行 `install.bat` 安装依赖包
2. 双击运行 `start.bat` 启动程序

#### 方法二：手动安装
```bash
pip install customtkinter fastapi uvicorn bcrypt requests pillow python-multipart pydantic
python main.py
```

#### 方法三：简化版本（无需额外依赖）
```bash
python simple_main.py
```

### 3. 默认登录信息
- 用户名: `admin`
- 密码: `admin123`

**重要：请在首次登录后修改默认密码！**

## 功能介绍

### 主要功能
1. **用户管理** - 支持多用户登录，管理员和普通用户权限分离
2. **文件夹管理** - 添加本地文件夹到系统中进行统一管理
3. **权限控制** - 为不同用户设置不同的文件夹访问权限
4. **下载管理** - 控制用户下载次数，记录下载日志
5. **文件浏览** - 浏览文件夹中的文件，支持预览和下载

### 界面说明

#### 登录界面
- 输入用户名和密码登录系统
- 支持回车键快速登录

#### 主界面
- **左侧边栏**: 显示所有可访问的文件夹列表
- **右侧主区域**: 显示选中文件夹中的文件列表
- **顶部工具栏**: 添加文件夹、刷新等操作按钮

## 使用流程

### 1. 管理员操作

#### 添加文件夹
1. 点击"添加文件夹"按钮
2. 选择要管理的本地文件夹
3. 输入显示名称和描述
4. 确认添加

#### 用户管理
- 通过API接口创建新用户
- 设置用户权限
- 查看用户活动日志

### 2. 普通用户操作

#### 浏览文件
1. 从左侧文件夹列表选择文件夹
2. 右侧显示文件夹中的所有文件
3. 可以看到文件名、大小、类型等信息

#### 下载文件
1. 在文件列表中找到要下载的文件
2. 点击"下载"按钮
3. 选择保存位置
4. 系统会记录下载日志

#### 查看文件
1. 点击"查看"按钮
2. 使用系统默认程序打开文件

## 高级功能

### API接口
程序提供完整的REST API接口，运行时可访问：
- API文档: `http://localhost:8000/docs`
- 接口地址: `http://localhost:8000/api/v1/`

### 主要API端点
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/folders/` - 获取文件夹列表
- `POST /api/v1/folders/` - 添加文件夹
- `GET /api/v1/folders/{id}/files` - 获取文件列表
- `POST /api/v1/permissions/` - 设置权限

### 数据库结构
程序使用SQLite数据库存储数据，位于 `database/newnet.db`

#### 主要数据表
- `users` - 用户信息
- `folders` - 文件夹信息
- `permissions` - 权限设置
- `download_logs` - 下载日志

## 故障排除

### 常见问题

#### 1. 程序无法启动
- 检查Python版本是否为3.7+
- 确认所有依赖包已安装
- 尝试运行简化版本 `python simple_main.py`

#### 2. 登录失败
- 确认用户名密码正确
- 检查数据库文件是否存在
- 尝试删除数据库文件重新初始化

#### 3. 文件夹添加失败
- 确认文件夹路径存在且可访问
- 检查是否有足够的权限
- 确认文件夹路径未重复添加

#### 4. API服务器启动失败
- 检查端口8000是否被占用
- 确认防火墙设置
- 查看错误日志信息

### 日志查看
程序运行时会在控制台输出详细日志信息，包括：
- 数据库操作日志
- API请求日志
- 错误信息

### 数据备份
重要数据存储在以下位置：
- 数据库文件: `database/newnet.db`
- 配置文件: `config/settings.py`

建议定期备份这些文件。

## 技术支持

### 系统要求
- 操作系统: Windows 10/11
- Python: 3.7+
- 内存: 最少512MB
- 硬盘: 最少100MB可用空间

### 性能优化
- 避免添加包含大量文件的文件夹
- 定期清理下载日志
- 合理设置用户权限

### 安全建议
- 修改默认管理员密码
- 定期更新用户密码
- 限制网络访问权限
- 定期备份数据

## 版本信息
- 当前版本: v1.0.0
- 发布日期: 2024年
- 开发语言: Python
- 界面框架: CustomTkinter / Tkinter
- 数据库: SQLite3

## 联系方式
如有问题或建议，请联系开发团队。
