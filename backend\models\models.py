"""
数据模型定义
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel

# 用户模型
class User(BaseModel):
    id: Optional[int] = None
    username: str
    password_hash: str
    is_admin: bool = False
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None

class UserCreate(BaseModel):
    username: str
    password: str
    is_admin: bool = False

class UserLogin(BaseModel):
    username: str
    password: str

# 文件夹模型
class Folder(BaseModel):
    id: Optional[int] = None
    name: str
    path: str
    description: Optional[str] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    created_by: Optional[int] = None

class FolderCreate(BaseModel):
    name: str
    path: str
    description: Optional[str] = None

class FolderUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

# 权限模型
class Permission(BaseModel):
    id: Optional[int] = None
    user_id: int
    folder_id: int
    can_read: bool = True
    can_download: bool = False
    download_limit: Optional[int] = None
    download_count: int = 0
    created_at: Optional[datetime] = None

class PermissionCreate(BaseModel):
    user_id: int
    folder_id: int
    can_read: bool = True
    can_download: bool = False
    download_limit: Optional[int] = None

class PermissionUpdate(BaseModel):
    can_read: Optional[bool] = None
    can_download: Optional[bool] = None
    download_limit: Optional[int] = None

# 下载记录模型
class DownloadLog(BaseModel):
    id: Optional[int] = None
    user_id: int
    folder_id: int
    file_path: str
    download_time: Optional[datetime] = None
    ip_address: Optional[str] = None

class DownloadLogCreate(BaseModel):
    user_id: int
    folder_id: int
    file_path: str
    ip_address: Optional[str] = None

# API响应模型
class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None

class LoginResponse(BaseModel):
    success: bool
    message: str
    user_id: Optional[int] = None
    username: Optional[str] = None
    is_admin: Optional[bool] = None
