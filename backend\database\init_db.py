"""
数据库初始化模块
"""
import sqlite3
import bcrypt
from pathlib import Path
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from config.settings import DATABASE_PATH, DATABASE_DIR

class DatabaseInitializer:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.ensure_database_dir()
    
    def ensure_database_dir(self):
        """确保数据库目录存在"""
        DATABASE_DIR.mkdir(exist_ok=True)
    
    def init_database(self):
        """初始化数据库"""
        try:
            # 检查数据库是否已存在
            if self.db_path.exists():
                print(f"数据库已存在: {self.db_path}")
                return True
            
            # 创建数据库连接
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建用户表
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            
            # 创建文件夹表
            cursor.execute('''
                CREATE TABLE folders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    path TEXT UNIQUE NOT NULL,
                    description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            
            # 创建权限表
            cursor.execute('''
                CREATE TABLE permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    folder_id INTEGER NOT NULL,
                    can_read BOOLEAN DEFAULT TRUE,
                    can_download BOOLEAN DEFAULT FALSE,
                    download_limit INTEGER,
                    download_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (folder_id) REFERENCES folders (id),
                    UNIQUE(user_id, folder_id)
                )
            ''')
            
            # 创建下载日志表
            cursor.execute('''
                CREATE TABLE download_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    folder_id INTEGER NOT NULL,
                    file_path TEXT NOT NULL,
                    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (folder_id) REFERENCES folders (id)
                )
            ''')
            
            # 创建默认管理员用户
            self.create_default_admin(cursor)
            
            # 提交更改并关闭连接
            conn.commit()
            conn.close()
            
            print(f"数据库初始化成功: {self.db_path}")
            return True
            
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            return False
    
    def create_default_admin(self, cursor):
        """创建默认管理员用户"""
        # 默认管理员密码
        default_password = "admin123"
        password_hash = bcrypt.hashpw(default_password.encode('utf-8'), bcrypt.gensalt())
        
        cursor.execute('''
            INSERT INTO users (username, password_hash, is_admin)
            VALUES (?, ?, ?)
        ''', ("admin", password_hash.decode('utf-8'), True))
        
        print("默认管理员用户已创建:")
        print("用户名: admin")
        print("密码: admin123")
        print("请在首次登录后修改密码!")

def initialize_database():
    """数据库初始化入口函数"""
    initializer = DatabaseInitializer()
    return initializer.init_database()

if __name__ == "__main__":
    initialize_database()
