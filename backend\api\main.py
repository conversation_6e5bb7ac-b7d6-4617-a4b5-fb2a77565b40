"""
FastAPI主应用程序
"""
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import sys
import os
import threading

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from config.settings import API_HOST, API_PORT, API_PREFIX
from backend.api import auth, folders, permissions

# 创建FastAPI应用
app = FastAPI(
    title="NewNet File Manager API",
    description="文件管理系统API接口",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix=API_PREFIX)
app.include_router(folders.router, prefix=API_PREFIX)
app.include_router(permissions.router, prefix=API_PREFIX)

@app.get("/")
async def root():
    """根路径"""
    return {"message": "NewNet File Manager API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

class APIServer:
    """API服务器类"""
    def __init__(self):
        self.server = None
        self.thread = None
        self.is_running = False

    def start(self):
        """启动API服务器"""
        if self.is_running:
            return

        def run_server():
            try:
                import uvicorn
                uvicorn.run(
                    app,
                    host=API_HOST,
                    port=API_PORT,
                    log_level="error"  # 减少日志输出
                )
            except Exception as e:
                print(f"API服务器启动失败: {e}")

        self.thread = threading.Thread(target=run_server, daemon=True)
        self.thread.start()
        self.is_running = True
        print(f"API服务器已启动: http://{API_HOST}:{API_PORT}")

    def stop(self):
        """停止API服务器"""
        if self.is_running:
            self.is_running = False
            print("API服务器已停止")

# 全局API服务器实例
api_server = APIServer()

if __name__ == "__main__":
    # 直接运行API服务器
    uvicorn.run(
        app,
        host=API_HOST,
        port=API_PORT,
        log_level="info"
    )
