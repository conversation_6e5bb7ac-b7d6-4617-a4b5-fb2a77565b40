"""
NewNet File Manager - 主程序入口
"""
import sys
import os
import threading
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

# 导入配置和模块
from config.settings import ensure_directories
from backend.database.init_db import initialize_database
from backend.api.main import api_server
from ui.login_window import LoginWindow
from ui.main_window import MainWindow

class NewNetApplication:
    """NewNet文件管理器应用程序主类"""
    
    def __init__(self):
        self.user_data = None
        self.api_server_started = False
        
    def initialize(self):
        """初始化应用程序"""
        print("正在初始化 NewNet File Manager...")
        
        # 1. 确保必要的目录存在
        print("检查目录结构...")
        ensure_directories()
        
        # 2. 初始化数据库
        print("初始化数据库...")
        if not initialize_database():
            print("数据库初始化失败!")
            return False
        
        # 3. 启动API服务器
        print("启动API服务器...")
        try:
            api_server.start()
            self.api_server_started = True
            # 等待服务器启动
            time.sleep(2)
        except Exception as e:
            print(f"API服务器启动失败: {e}")
            return False
        
        print("初始化完成!")
        return True
    
    def show_login(self):
        """显示登录界面"""
        print("显示登录界面...")
        login_window = LoginWindow()
        self.user_data = login_window.show()
        return self.user_data is not None
    
    def show_main_window(self):
        """显示主界面"""
        if not self.user_data:
            print("用户数据为空，无法显示主界面")
            return
        
        print(f"欢迎 {self.user_data['username']}!")
        main_window = MainWindow(self.user_data)
        main_window.show()
    
    def cleanup(self):
        """清理资源"""
        if self.api_server_started:
            print("正在停止API服务器...")
            api_server.stop()
    
    def run(self):
        """运行应用程序"""
        try:
            # 初始化
            if not self.initialize():
                input("按回车键退出...")
                return
            
            # 显示登录界面
            if not self.show_login():
                print("登录取消或失败")
                return
            
            # 显示主界面
            self.show_main_window()
            
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行出错: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理资源
            self.cleanup()

def main():
    """主函数"""
    print("=" * 50)
    print("NewNet File Manager v1.0.0")
    print("现代化文件管理系统")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        input("按回车键退出...")
        return
    
    # 检查必要的依赖包
    try:
        import customtkinter
        import fastapi
        import uvicorn
        import bcrypt
        import requests
        import pydantic
    except ImportError as e:
        print(f"错误: 缺少必要的依赖包 - {e}")
        print("请运行: pip install -r requirements.txt")
        input("按回车键退出...")
        return
    
    # 创建并运行应用程序
    app = NewNetApplication()
    app.run()

if __name__ == "__main__":
    main()
